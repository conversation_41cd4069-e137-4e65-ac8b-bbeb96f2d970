const request = require('supertest');
const { EsimPlan, Country, User, Provider } = require('../../../src/models');
const sequelize = require('../../config/database');
const app = require('../../../app');
const bcrypt = require('bcrypt');

describe('EsimPlan Data Optimization Tests', () => {
    let server;
    let testPlan;
    let testCountry;
    let testProvider;
    let partnerToken;

    beforeAll(async () => {
        // Create test database and tables
        try {
            await sequelize.authenticate();
            await sequelize.sync({ force: true });
            server = app.listen(0); // Random port
        } catch (error) {
            console.error('Test setup failed:', error);
            throw error;
        }
    });

    afterAll(async () => {
        // Cleanup
        if (server) await server.close();
        await sequelize.close();
    });

    beforeEach(async () => {
        // Clear test data
        await Promise.all([
            EsimPlan.destroy({ where: {}, force: true }),
            Country.destroy({ where: {}, force: true }),
            User.destroy({ where: {}, force: true }),
            Provider.destroy({ where: {}, force: true })
        ]);

        // Create test data
        testCountry = await Country.create({
            name: 'Test Country',
            flagUrl: 'http://example.com/flag.png',
            flagEmoji: '🏳️'
        });

        testProvider = await Provider.create({
            name: 'Test Provider',
            type: 'Custom',
            status: 'active'
        });

        // Create a test plan with all fields populated
        testPlan = await EsimPlan.create({
            name: 'Complete Test Plan',
            productId: 'TEST123',
            description: 'Detailed test description',
            instructions: 'Test instructions',
            features: JSON.stringify(['5G Support', 'Unlimited Calls']),
            planInfo: 'Detailed plan information',
            providerMetadata: JSON.stringify({key: 'value'}),
            planType: 'Fixed',
            planData: '10',
            planDataUnit: 'GB',
            validityDays: 30,
            buyingPrice: 10.00,
            sellingPrice: 15.00,
            status: 'visible',
            networkName: 'Test Network',
            networkType: '4G/LTE',
            region: 'Europe',
            category: 'esim_realtime',
            voiceMin: 50,
            sms: 100,
            speed: 'Unrestricted',
            profile: 'local',
            activationPolicy: 'Activation upon purchase',
            startDateEnabled: false,
            isVoiceAvailable: true,
            isSmsAvailable: true,
            hotspotAvailable: true,
            topUpAvailable: false,
            providerId: testProvider.id
        });

        await testPlan.addCountry(testCountry);

        // Create a test partner user
        const hashedPassword = await bcrypt.hash('password123', 10);
        const partner = await User.create({
            email: '<EMAIL>',
            password: hashedPassword,
            role: 'partner',
            isActive: true,
            markupPercentage: 20
        });

        // Get auth token
        const authResponse = await request(server)
            .post('/api/auth/login')
            .send({
                email: '<EMAIL>',
                password: 'password123'
            });

        partnerToken = authResponse.body.token;
    });

    describe('Listing vs. Details Data Optimization', () => {
        it('should return limited fields for plans listing endpoint', async () => {
            // Test the listing endpoint
            const response = await request(server)
                .get('/api/esim-plans/partner')
                .set('Authorization', `Bearer ${partnerToken}`);

            expect(response.status).toBe(200);
            expect(response.body.plans).toHaveLength(1);
            
            // Get the plan from the response
            const plan = response.body.plans[0];
            
            // Essential fields that SHOULD be included in list view
            expect(plan).toHaveProperty('id');
            expect(plan).toHaveProperty('productId');
            expect(plan).toHaveProperty('name');
            expect(plan).toHaveProperty('networkName');
            expect(plan).toHaveProperty('buyingPrice');
            expect(plan).toHaveProperty('sellingPrice');
            expect(plan).toHaveProperty('validityDays');
            expect(plan).toHaveProperty('planData');
            expect(plan).toHaveProperty('planDataUnit');
            expect(plan).toHaveProperty('planType');
            expect(plan).toHaveProperty('region');
            expect(plan).toHaveProperty('category');
            
            // Fields that should NOT be included in list view for optimization
            expect(plan).not.toHaveProperty('description');
            expect(plan).not.toHaveProperty('instructions');
            expect(plan).not.toHaveProperty('planInfo');
            expect(plan).not.toHaveProperty('features');
            expect(plan).not.toHaveProperty('providerMetadata');
        });

        it('should return all fields for plan details endpoint', async () => {
            // Test the details endpoint
            const response = await request(server)
                .get(`/api/esim-plans/partner/${testPlan.id}`)
                .set('Authorization', `Bearer ${partnerToken}`);

            expect(response.status).toBe(200);
            
            // Essential fields that should be in both list and details
            expect(response.body).toHaveProperty('id');
            expect(response.body).toHaveProperty('productId');
            expect(response.body).toHaveProperty('name');
            expect(response.body).toHaveProperty('networkName');
            expect(response.body).toHaveProperty('buyingPrice');
            expect(response.body).toHaveProperty('sellingPrice');
            expect(response.body).toHaveProperty('validityDays');
            expect(response.body).toHaveProperty('planData');
            expect(response.body).toHaveProperty('planDataUnit');
            expect(response.body).toHaveProperty('planType');
            expect(response.body).toHaveProperty('region');
            expect(response.body).toHaveProperty('category');
            
            // Additional fields that should ONLY be in details view
            expect(response.body).toHaveProperty('description');
            expect(response.body).toHaveProperty('instructions');
            expect(response.body).toHaveProperty('planInfo');
            expect(response.body).toHaveProperty('features');
            expect(response.body).toHaveProperty('providerMetadata');
            expect(response.body).toHaveProperty('voiceMin');
            expect(response.body).toHaveProperty('sms');
            expect(response.body).toHaveProperty('speed');
            expect(response.body).toHaveProperty('profile');
            expect(response.body).toHaveProperty('activationPolicy');
            expect(response.body).toHaveProperty('isVoiceAvailable');
            expect(response.body).toHaveProperty('isSmsAvailable');
            expect(response.body).toHaveProperty('hotspotAvailable');
            expect(response.body).toHaveProperty('topUpAvailable');
            expect(response.body).toHaveProperty('startDateEnabled');
        });

        it('should verify the expected number of fields returned for both endpoints', async () => {
            // Get listing data
            const listResponse = await request(server)
                .get('/api/esim-plans/partner')
                .set('Authorization', `Bearer ${partnerToken}`);
            
            const listPlan = listResponse.body.plans[0];
            const listFieldCount = Object.keys(listPlan).length;
            
            // Get details data
            const detailsResponse = await request(server)
                .get(`/api/esim-plans/partner/${testPlan.id}`)
                .set('Authorization', `Bearer ${partnerToken}`);
            
            const detailsFieldCount = Object.keys(detailsResponse.body).length;
            
            // Verify that details has significantly more fields than list view
            expect(detailsFieldCount).toBeGreaterThan(listFieldCount);
            
            // The optimization we implemented should reduce the listing fields significantly
            // Here we're assuming list view has roughly 15 fields while details has 30+ fields
            expect(listFieldCount).toBeLessThan(20); // Listing should have less than 20 fields
            expect(detailsFieldCount).toBeGreaterThan(25); // Details should have more than 25 fields
            
            console.log(`List view fields: ${listFieldCount}, Details view fields: ${detailsFieldCount}`);
        });

        it('should demonstrate significant data size reduction for listing endpoint', async () => {
            // Create multiple plans to simulate a real listing scenario
            const plans = [];
            for (let i = 1; i <= 5; i++) {
                const plan = await EsimPlan.create({
                    name: `Test Plan ${i}`,
                    productId: `TEST${i}`,
                    description: `Detailed description for plan ${i} with lots of text to demonstrate size differences. This is a very long description that would normally be transferred in the API response but is not needed for the listing view. Lorem ipsum dolor sit amet, consectetur adipiscing elit.`,
                    instructions: `Very long installation instructions for plan ${i}. Step 1: Do this. Step 2: Do that. Step 3: Do something else. These instructions are lengthy and not needed for the listing view.`,
                    features: JSON.stringify([`Feature ${i}A`, `Feature ${i}B`, `Feature ${i}C`]),
                    planInfo: `Comprehensive plan information for plan ${i} with detailed specifications and technical details.`,
                    providerMetadata: JSON.stringify({key: `value${i}`, data: `metadata${i}`, config: `config${i}`}),
                    planType: 'Fixed',
                    planData: String(i * 2),
                    planDataUnit: 'GB',
                    validityDays: 30 + i,
                    buyingPrice: 10.00 + i,
                    sellingPrice: 15.00 + i,
                    status: 'visible',
                    networkName: `Network ${i}`,
                    networkType: '4G/LTE',
                    region: 'Europe',
                    category: 'esim_realtime',
                    voiceMin: 50 + i * 10,
                    sms: 100 + i * 20,
                    speed: 'Unrestricted',
                    profile: 'local',
                    activationPolicy: 'Activation upon purchase',
                    startDateEnabled: i % 2 === 0,
                    isVoiceAvailable: true,
                    isSmsAvailable: true,
                    hotspotAvailable: true,
                    topUpAvailable: i % 3 === 0,
                    providerId: testProvider.id
                });

                await plan.addCountry(testCountry);
                plans.push(plan);
            }

            // Get listing data
            const listResponse = await request(server)
                .get('/api/esim-plans/partner')
                .set('Authorization', `Bearer ${partnerToken}`);

            // Get details for the first plan
            const detailsResponse = await request(server)
                .get(`/api/esim-plans/partner/${plans[0].id}`)
                .set('Authorization', `Bearer ${partnerToken}`);

            // Calculate response sizes (approximate JSON string lengths)
            const listResponseSize = JSON.stringify(listResponse.body).length;
            const detailsResponseSize = JSON.stringify(detailsResponse.body).length;
            
            // The listing response should be significantly smaller per plan
            // even though it contains multiple plans vs single plan in details
            const plansInListing = listResponse.body.plans.length;
            const averagePlanSizeInListing = listResponseSize / plansInListing;
            
            console.log(`Listing response size: ${listResponseSize} bytes for ${plansInListing} plans`);
            console.log(`Details response size: ${detailsResponseSize} bytes for 1 plan`);
            console.log(`Average plan size in listing: ${Math.round(averagePlanSizeInListing)} bytes`);
            console.log(`Single plan in details: ${detailsResponseSize} bytes`);
            console.log(`Size reduction: ${Math.round((1 - averagePlanSizeInListing / detailsResponseSize) * 100)}%`);
            
            // Verify that the listing per-plan data is significantly smaller
            expect(averagePlanSizeInListing).toBeLessThan(detailsResponseSize * 0.6); // At least 40% reduction
            
            expect(listResponse.status).toBe(200);
            expect(detailsResponse.status).toBe(200);
            expect(listResponse.body.plans).toHaveLength(5);
        });

        it('should verify performance characteristics of both endpoints', async () => {
            const startTime = Date.now();
            
            // Test listing endpoint response time
            const listStart = Date.now();
            const listResponse = await request(server)
                .get('/api/esim-plans/partner')
                .set('Authorization', `Bearer ${partnerToken}`);
            const listTime = Date.now() - listStart;
            
            // Test details endpoint response time
            const detailsStart = Date.now();
            const detailsResponse = await request(server)
                .get(`/api/esim-plans/partner/${testPlan.id}`)
                .set('Authorization', `Bearer ${partnerToken}`);
            const detailsTime = Date.now() - detailsStart;
            
            console.log(`Listing endpoint response time: ${listTime}ms`);
            console.log(`Details endpoint response time: ${detailsTime}ms`);
            
            // Both endpoints should respond reasonably quickly
            expect(listTime).toBeLessThan(1000); // Should respond in under 1 second
            expect(detailsTime).toBeLessThan(1000); // Should respond in under 1 second
            
            expect(listResponse.status).toBe(200);
            expect(detailsResponse.status).toBe(200);
        });
    });
});
