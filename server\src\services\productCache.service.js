const { EsimPlan, Country, Provider } = require('../models');
const { Op } = require('sequelize');
const { getCachedPlan, setCachePlan, invalidatePlanCache } = require('../utils/cacheManager');
const optimizedCache = require('../utils/optimizedCacheManager');
const memoryMonitor = require('../utils/memoryMonitor');

class ProductCacheService {
    constructor() {
        this.isWarming = false;
        this.lastWarmTime = null;
        this.warmingInterval = 5 * 60 * 1000; // 5 minutes
    }

    /**
     * Generate cache key for products API
     */
    generateCacheKey(partnerId, region = 'all', country = 'all') {
        return `api_v1_products_${partnerId}_${region}_${country}`;
    }

    /**
     * Get optimized product data from database (MEMORY SAFE VERSION)
     */
    async getProductsFromDB(whereClause, countryInclude) {
        console.log('🔍 Using memory-optimized database query...');

        const { sequelize } = require('../models');

        try {
            // Step 1: Get basic product data without heavy fields
            const products = await sequelize.query(`
                SELECT
                    p.id, p.productId, p.name, p.description, p.planInfo,
                    p.sellingPrice, p.buyingPrice, p.validityDays, p.region,
                    p.planData, p.planDataUnit, p.planType, p.category, p.networkType,
                    p.is_voice, p.is_sms, p.hotspot, p.top_up, p.profile,
                    p.activationPolicy, p.startDateEnabled, p.providerMetadata,
                    p.customPlanData, p.voiceMin, p.voiceMinUnit, p.sms, p.speed,
                    pr.name as providerName
                FROM esimplans p
                LEFT JOIN providers pr ON p.providerId = pr.id
                WHERE p.status = 'visible'
                    AND p.isActive = true
                    AND p.category IN ('esim_realtime', 'esim_addon')
                ORDER BY p.createdAt DESC
            `, {
                type: sequelize.QueryTypes.SELECT
            });

            console.log(`📊 Loaded ${products.length} products (memory-safe mode)`);

            // Step 2: Load countries data efficiently for all products at once
            if (products.length > 0) {
                const productIds = products.map(p => p.id);

                // Get countries for all products in a single query
                const productCountries = await sequelize.query(`
                    SELECT epc.esimPlanId, c.id as countryId
                    FROM esimplancountries epc
                    JOIN countries c ON epc.countryId = c.id
                    WHERE epc.esimPlanId IN (:productIds)
                `, {
                    replacements: { productIds },
                    type: sequelize.QueryTypes.SELECT
                });

                const countriesMap = new Map();
                productCountries.forEach(pc => {
                    if (!countriesMap.has(pc.esimPlanId)) {
                        countriesMap.set(pc.esimPlanId, []);
                    }
                    countriesMap.get(pc.esimPlanId).push({ id: pc.countryId });
                });

                // Step 3: Add minimal required fields
                products.forEach(product => {
                    product.countries = countriesMap.get(product.id) || [];
                    product.provider = { name: product.providerName };
                    product.features = []; 
                    product.instructions = product.instructions || '';

                    if (product.providerName === 'Mobimatter' || product.providerName === 'Billionconnect') {
                        if (product.providerMetadata) {
                            try {
                                const metadata = typeof product.providerMetadata === 'string'
                                    ? JSON.parse(product.providerMetadata)
                                    : product.providerMetadata;

                                // Keep only essential data for countries
                                product.providerMetadata = {
                                    originalData: {
                                        supportedCountries: metadata.originalData?.supportedCountries || []
                                    }
                                };
                            } catch (e) {
                                console.warn(`Failed to parse providerMetadata for product ${product.productId}:`, e);
                                product.providerMetadata = null;
                            }
                        } else {
                            product.providerMetadata = null;
                        }
                    } else {
                        product.providerMetadata = null;
                    }

                    delete product.providerName;
                    delete product.id;
                });
            }

            return products;

        } catch (error) {
            console.error('Error in memory-safe query:', error);
            return await this.getProductsFromDBLightweight();
        }
    }

    /**
     * Transform products for API response - OPTIMIZED VERSION
     * Removes only features array, handles conditional fields smartly
     */
    transformProducts(products, partner) {
        return products.map(product => {
            let price = product.sellingPrice;
            if (price === null && partner?.markupPercentage) {
                const markup = parseFloat(partner.markupPercentage) / 100;
                price = parseFloat(product.buyingPrice) * (1 + markup);
                price = Math.round(price * 100) / 100;
            }
            
            // Get countries efficiently
            let countries = [];
            if (product.provider?.name === 'Mobimatter') {
                countries = product.providerMetadata?.originalData?.supportedCountries || [];
            } else {
                countries = product.countries ? product.countries.map(c => c.id) : [];
            }
            
            // Build base response with all essential fields
            const response = {
                productId: product.productId,
                name: product.name,
                description: product.description,
                price: price,
                validityDays: product.validityDays,
                countries: countries,
                region: typeof product.region === 'string'
                    ? product.region.split(',').map(r => r.trim())
                    : product.region,
                dataAmount: product.planData,
                dataUnit: product.planDataUnit,
                speed: product.speed,
                planType: product.planType,
                category: product.category,
                networkType: product.networkType,
                isVoiceAvailable: product.is_voice === 'Available',
                isSmsAvailable: product.is_sms === 'Available',
                hotspotAvailable: product.hotspot === 'Available',
                topUpAvailable: product.top_up === 'Available',
                profile: product.profile,
                activationPolicy: product.activationPolicy,
                startDateEnabled: product.startDateEnabled
            };

            if (product.planInfo && product.planInfo.trim()) {
                response.planInfo = product.planInfo;
            }

            if (product.instructions && product.instructions.trim()) {
                response.instructions = product.instructions;
            }

            if (product.customPlanData && product.customPlanData.trim()) {
                response.customPlanData = product.customPlanData;
            }

            // Handle voiceMin - convert to number if it's a string
            const voiceMinValue = product.voiceMin ? parseFloat(product.voiceMin) : null;
            if (voiceMinValue !== null && !isNaN(voiceMinValue) && voiceMinValue > 0) {
                response.voiceMin = voiceMinValue;
                response.voiceMinUnit = product.voiceMinUnit || 'Min'; 
            }

            // Handle sms - convert to number if it's a string  
            const smsValue = product.sms ? parseInt(product.sms) : null;
            if (smsValue !== null && !isNaN(smsValue) && smsValue > 0) {
                response.sms = smsValue;
            }
            return response;
        });
    }

    /**
     * Warm shared base cache (much more efficient)
     */
    async warmCache() {
        if (this.isWarming) {
            console.log('🔥 Cache warming already in progress, skipping...');
            return;
        }

        this.isWarming = true;
        const startTime = Date.now();

        try {
            console.log('🔥 Starting smart product cache warming...');
            memoryMonitor.logMemoryStatus();

            // Only cache the BASE product data once
            const baseWhere = {
                status: 'visible',
                isActive: true,
                category: {
                    [Op.in]: ['esim_realtime', 'esim_addon']
                }
            };

            const countryInclude = {
                model: Country,
                as: 'countries',
                attributes: ['id', 'name'],
                through: { attributes: [] },
                required: false
            };

            // Cache the base product data 
            const baseCacheKey = 'base_products_data';
            if (!getCachedPlan(baseCacheKey)) {
                const allProducts = await this.getProductsFromDB(baseWhere, countryInclude);
                console.log(`🔥 Retrieved ${allProducts.length} products for base cache`);

                // Cache base products for 10 minutes 
                setCachePlan(baseCacheKey, allProducts, 600 * 1000);
                console.log('🔥 Base product cache warmed');
            } else {
                console.log('🔥 Base product cache already warm');
            }

            this.lastWarmTime = Date.now();
            const duration = Date.now() - startTime;

            console.log(`🔥 Smart cache warming completed in ${duration}ms`);
            console.log('💡 Partner-specific caches will be generated on-demand');
            memoryMonitor.logMemoryStatus();

        } catch (error) {
            console.error('🔥 Error during cache warming:', error);
        } finally {
            this.isWarming = false;
        }
    }

    /**
     * Get base products from cache or database (MEMORY OPTIMIZED)
     */
    async getBaseProducts() {
        const baseCacheKey = 'base_products_data';
        let baseProducts = getCachedPlan(baseCacheKey);

        if (!baseProducts) {
            console.log('💾 Base cache miss, fetching from database with memory optimization...');
            memoryMonitor.logMemoryStatus();

            console.log('🛡️ Using lightweight query by default to prevent memory leaks...');

            try {
                baseProducts = await this.getProductsFromDBLightweight();
                console.log(`✅ Loaded ${baseProducts.length} products (lightweight mode)`);
                memoryMonitor.logMemoryStatus();

                setCachePlan(baseCacheKey, baseProducts, 180 * 1000); // 3 minutes

            } catch (error) {
                console.error('Error loading products from lightweight query:', error);
                baseProducts = [];
                console.log('🚨 Returning empty product array to prevent crash');
            }
        } else {
            console.log('🚀 Base cache HIT - using cached products');
        }

        return baseProducts;
    }

    /**
     * Lightweight product loading for memory-constrained situations
     */
    async getProductsFromDBLightweight() {
        console.log('🪶 Using ultra-lightweight product query for memory safety...');

        const { sequelize } = require('../models');

        try {
            const products = await sequelize.query(`
                SELECT
                    p.id, p.productId, p.name, p.description, p.planInfo,
                    p.sellingPrice, p.buyingPrice, p.validityDays, p.region,
                    p.planData, p.planDataUnit, p.planType, p.category, p.networkType,
                    p.is_voice, p.is_sms, p.hotspot, p.top_up, p.profile,
                    p.activationPolicy, p.startDateEnabled, p.providerMetadata,
                    p.customPlanData, p.voiceMin, p.voiceMinUnit, p.sms, p.speed,
                    pr.name as providerName
                FROM esimplans p
                LEFT JOIN providers pr ON p.providerId = pr.id
                WHERE p.status = 'visible'
                    AND p.isActive = true
                    AND p.category IN ('esim_realtime', 'esim_addon')
                ORDER BY p.createdAt DESC
            `, {
                type: sequelize.QueryTypes.SELECT
            });

            console.log(`🪶 Loaded ${products.length} products (lightweight mode)`);

            if (products.length > 0) {
                const productIds = products.map(p => p.id);

                const productCountries = await sequelize.query(`
                    SELECT epc.esimPlanId, c.id as countryId
                    FROM esimplancountries epc
                    JOIN countries c ON epc.countryId = c.id
                    WHERE epc.esimPlanId IN (:productIds)
                `, {
                    replacements: { productIds },
                    type: sequelize.QueryTypes.SELECT
                });

                const countriesMap = new Map();
                productCountries.forEach(pc => {
                    if (!countriesMap.has(pc.esimPlanId)) {
                        countriesMap.set(pc.esimPlanId, []);
                    }
                    countriesMap.get(pc.esimPlanId).push({ id: pc.countryId });
                });

                products.forEach(product => {
                    product.countries = countriesMap.get(product.id) || [];
                    product.provider = { name: product.providerName };
                    product.features = []; // Empty features to save memory

                    if (product.providerName === 'Mobimatter' || product.providerName === 'Billionconnect') {
                        if (product.providerMetadata) {
                            try {
                                const metadata = typeof product.providerMetadata === 'string'
                                    ? JSON.parse(product.providerMetadata)
                                    : product.providerMetadata;

                                product.providerMetadata = {
                                    originalData: {
                                        supportedCountries: metadata.originalData?.supportedCountries || []
                                    }
                                };
                            } catch (e) {
                                console.warn(`Failed to parse providerMetadata for product ${product.productId}:`, e);
                                product.providerMetadata = null;
                            }
                        } else {
                            product.providerMetadata = null;
                        }
                    } else {
                        product.providerMetadata = null;
                    }

                    delete product.providerName;
                    delete product.id; 
                });
            }

            return products;

        } catch (error) {
            console.error('Error in lightweight query:', error);
            return [];
        }
    }

    /**
     * Invalidate all product caches
     */
    invalidateAllProductCaches() {
        console.log('🗑️ Invalidating all product caches...');
        
        const patterns = ['api_v1_products_'];
        patterns.forEach(pattern => {
            invalidatePlanCache(pattern);
        });
        
        console.log('🗑️ Product cache invalidation completed');
    }

    /**
     * Start automatic cache warming (DISABLED - too memory intensive)
     */
    startAutoWarming() {
        console.log('🔥 Automatic cache warming DISABLED (memory optimization)');
        console.log('💡 Cache will be populated on-demand when first API request is made');
    }

    /**
     * Get cache statistics
     */
    getCacheStats() {
        return {
            isWarming: this.isWarming,
            lastWarmTime: this.lastWarmTime,
            warmingInterval: this.warmingInterval
        };
    }
}

const productCacheService = new ProductCacheService();

module.exports = productCacheService;
