const { Server } = require('socket.io');
const jwt = require('jsonwebtoken');
const { User } = require('../models');

class WebSocketService {
    constructor() {
        this.io = null;
        this.connectedUsers = new Map(); 
    }

    initialize(server) {
        const allowedOrigins = [
            'http://localhost:3000',
            'http://localhost:5000',
            'http://localhost:5173',  
            'https://esim.vizlync.net',
            'https://api.vizlync.net',
            'https://esim-management.vercel.app',
            'https://esim-management.onrender.com'
        ];

        this.io = new Server(server, {
            cors: {
                origin: allowedOrigins,
                methods: ["GET", "POST"],
                credentials: true
            },
            // Add connection state recovery to handle reconnections better
            connectionStateRecovery: {
                // the backup duration of the sessions and the packets
                maxDisconnectionDuration: 2 * 60 * 1000,
                // whether to skip middlewares upon successful recovery
                skipMiddlewares: true,
            },
            // Optimize ping timeout and interval to detect disconnections faster
            pingTimeout: 30000,
            pingInterval: 25000,
            // Prevent multiple connections from the same client
            allowEIO3: true,
            transports: ['websocket', 'polling'],
        });

        // Authentication middleware for WebSocket
        this.io.use(async (socket, next) => {
            try {
                const token = socket.handshake.auth.token;
                if (!token) {
                    return next(new Error('Authentication error: No token provided'));
                }

                const decoded = jwt.verify(token, process.env.JWT_SECRET);
                const user = await User.findByPk(decoded.id);

                if (!user) {
                    return next(new Error('Authentication error: User not found'));
                }

                socket.userId = user.id;
                socket.userRole = user.role;
                next();
            } catch (error) {
                next(new Error('Authentication error: Invalid token'));
            }
        });

        this.io.on('connection', (socket) => {
            console.log(`User ${socket.userId} (${socket.userRole}) connected via WebSocket`);

            // Store the connection
            this.connectedUsers.set(socket.userId, socket);

            // Join role-specific rooms
            socket.join(socket.userRole);

            // Handle disconnection
            socket.on('disconnect', () => {
                console.log(`User ${socket.userId} disconnected from WebSocket`);
                this.connectedUsers.delete(socket.userId);
            });

            // Handle user room joining (for wallet updates, etc.)
            socket.on('join', (userId) => {
                if (!userId) return;

                // Get all socket instances for this user
                const userRoom = `user_${userId}`;
                const roomSockets = this.io.sockets.adapter.rooms.get(userRoom);

                // If this user already has an active socket connection
                if (roomSockets && roomSockets.size > 0) {
                    console.log(`User ${userId} already has ${roomSockets.size} active connections`);
                }

                // Join the room anyway
                socket.join(userRoom);
                console.log(`User ${userId} joined their room`);
            });

            // Handle admin room joining
            socket.on('join-admin-room', () => {
                if (socket.userRole === 'admin') {
                    socket.join('admin-room');
                    console.log(`Admin user ${socket.userId} joined admin room`);
                }
            });

            // Handle partner room joining
            socket.on('join-partner-room', () => {
                if (socket.userRole === 'partner') {
                    socket.join('partner-room');
                    console.log(`Partner user ${socket.userId} joined partner room`);
                }
            });

            // Handle plan visibility updates (admin only)
            socket.on('plan-visibility-changed', (data) => {
                if (socket.userRole === 'admin') {
                    this.broadcastPlanVisibilityChange({
                        ...data,
                        initiator: 'admin' // Mark as admin-initiated
                    });
                }
            });
        });

        console.log('WebSocket service initialized');
    }

    // Broadcast plan visibility changes to all partners and admins
    broadcastPlanVisibilityChange(data) {
        if (!this.io) return;

        console.log('Broadcasting plan visibility change:', data);

        // Determine if this should show notifications to partners
        // Don't show notifications for admin-initiated changes
        const showPartnerNotification = data.initiator !== 'admin';

        // Send to all partners
        this.io.to('partner').emit('plan-visibility-updated', {
            type: data.type, // 'single' or 'provider'
            planId: data.planId,
            providerId: data.providerId,
            status: data.status,
            initiator: data.initiator || 'system', // 'admin', 'system', 'sync', etc.
            showNotification: showPartnerNotification,
            timestamp: Date.now()
        });

        // Also send to partner-room for plans page specifically
        this.io.to('partner-room').emit('plan-visibility-updated', {
            type: data.type,
            planId: data.planId,
            providerId: data.providerId,
            status: data.status,
            initiator: data.initiator || 'system',
            showNotification: showPartnerNotification,
            timestamp: Date.now()
        });

        // Send to all admins for real-time UI updates (admins always get notifications)
        this.io.to('admin').emit('provider-visibility-updated', {
            type: data.type,
            planId: data.planId,
            providerId: data.providerId,
            status: data.status,
            initiator: data.initiator || 'system',
            showNotification: true, // Admins always see notifications
            timestamp: Date.now()
        });

        // Also send to admin-room for providers page specifically
        this.io.to('admin-room').emit('provider-visibility-updated', {
            type: data.type,
            planId: data.planId,
            providerId: data.providerId,
            status: data.status,
            initiator: data.initiator || 'system',
            showNotification: true, // Admins always see notifications
            timestamp: Date.now()
        });
    }

    // Broadcast plan updates (for future use)
    broadcastPlanUpdate(planData) {
        if (!this.io) return;

        this.io.to('partner').emit('plan-updated', {
            plan: planData,
            timestamp: Date.now()
        });
    }

    // Broadcast new plans (for future use)
    broadcastNewPlan(planData) {
        if (!this.io) return;

        this.io.to('partner').emit('plan-added', {
            plan: planData,
            timestamp: Date.now()
        });
    }

    // Get connected users count
    getConnectedUsersCount() {
        return this.connectedUsers.size;
    }

    // Get connected users by role
    getConnectedUsersByRole(role) {
        const users = [];
        this.connectedUsers.forEach((socket, userId) => {
            if (socket.userRole === role) {
                users.push(userId);
            }
        });
        return users;
    }

    // Get the io instance for backward compatibility
    getIO() {
        return this.io;
    }
}

module.exports = new WebSocketService();
