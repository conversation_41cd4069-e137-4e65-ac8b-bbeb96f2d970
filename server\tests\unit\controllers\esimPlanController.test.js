const request = require('supertest');
const { EsimPlan, Country, User, Provider } = require('../../../src/models');
const sequelize = require('../../config/database');
const app = require('../../../app');
const bcrypt = require('bcrypt');

describe('EsimPlan Controller Tests', () => {
    let server;

    beforeAll(async () => {
        // Create test database and tables
        try {
            await sequelize.authenticate();
            await sequelize.sync({ force: true });
            server = app.listen(0); // Random port
        } catch (error) {
            console.error('Test setup failed:', error);
            throw error;
        }
    });

    afterAll(async () => {
        // Cleanup
        if (server) await server.close();
        await sequelize.close();
    });

    beforeEach(async () => {
        // Clear test data
        await Promise.all([
            EsimPlan.destroy({ where: {}, force: true }),
            Country.destroy({ where: {}, force: true }),
            User.destroy({ where: {}, force: true }),
            Provider.destroy({ where: {}, force: true })
        ]);
    });

    describe('GET /api/esim-plans/partner', () => {
        it('should return all visible plans for partner', async () => {
            // Create test data
            const testCountry = await Country.create({
                name: 'Test Country',
                flagUrl: 'http://example.com/flag.png',
                flagEmoji: '🏳️'
            });

            const testPlan = await EsimPlan.create({
                name: 'Test Plan',
                productId: 'TEST123',
                description: 'Test Description',
                planType: 'Fixed',
                planData: '10',
                planDataUnit: 'GB',
                validityDays: 30,
                buyingPrice: 10.00,
                sellingPrice: 15.00,
                status: 'visible',
                networkName: 'Test Network'
            });

            await testPlan.addCountry(testCountry);

            // Create a test partner user with hashed password
            const hashedPassword = await bcrypt.hash('password123', 10);
            const partner = await User.create({
                email: '<EMAIL>',
                password: hashedPassword,
                role: 'partner',
                isActive: true,
                markupPercentage: 20
            });

            // Get auth token
            const authResponse = await request(server)
                .post('/api/auth/login')
                .send({
                    email: '<EMAIL>',
                    password: 'password123'
                });

            expect(authResponse.status).toBe(200);
            const token = authResponse.body.token;

            // Test the endpoint
            const response = await request(server)
                .get('/api/esim-plans/partner')
                .set('Authorization', `Bearer ${token}`);

            expect(response.status).toBe(200);
            expect(response.body).toHaveProperty('plans');
            expect(response.body.plans).toHaveLength(1);
            expect(response.body.plans[0]).toHaveProperty('name', 'Test Plan');
            expect(response.body.countries).toHaveLength(1);
            expect(response.body.countries[0]).toHaveProperty('flagUrl');
        });

        it('should filter plans by country', async () => {
            // Create test countries
            const country1 = await Country.create({
                name: 'Country 1',
                flagUrl: 'http://example.com/flag1.png'
            });

            const country2 = await Country.create({
                name: 'Country 2',
                flagUrl: 'http://example.com/flag2.png'
            });

            // Create test plans
            const plan1 = await EsimPlan.create({
                name: 'Plan 1',
                productId: 'TEST1',
                status: 'visible',
                networkName: 'Network 1'
            });

            const plan2 = await EsimPlan.create({
                name: 'Plan 2',
                productId: 'TEST2',
                status: 'visible',
                networkName: 'Network 2'
            });

            await plan1.addCountry(country1);
            await plan2.addCountry(country2);

            // Create test partner with hashed password
            const hashedPassword = await bcrypt.hash('password123', 10);
            const partner = await User.create({
                email: '<EMAIL>',
                password: hashedPassword,
                role: 'partner',
                isActive: true
            });

            // Get auth token
            const authResponse = await request(server)
                .post('/api/auth/login')
                .send({
                    email: '<EMAIL>',
                    password: 'password123'
                });

            expect(authResponse.status).toBe(200);
            const token = authResponse.body.token;

            // Test filtering by country
            const response = await request(server)
                .get(`/api/esim-plans/partner?countryId=${country1.id}`)
                .set('Authorization', `Bearer ${token}`);

            expect(response.status).toBe(200);
            expect(response.body.plans).toHaveLength(1);
            expect(response.body.plans[0].name).toBe('Plan 1');
        });

        it('should filter plans by region', async () => {
            // Create test plan with region
            const plan = await EsimPlan.create({
                name: 'Regional Plan',
                productId: 'TEST-REG',
                status: 'visible',
                networkName: 'Network',
                region: 'Europe'
            });

            // Create test partner with hashed password
            const hashedPassword = await bcrypt.hash('password123', 10);
            const partner = await User.create({
                email: '<EMAIL>',
                password: hashedPassword,
                role: 'partner',
                isActive: true
            });

            // Get auth token
            const authResponse = await request(server)
                .post('/api/auth/login')
                .send({
                    email: '<EMAIL>',
                    password: 'password123'
                });

            expect(authResponse.status).toBe(200);
            const token = authResponse.body.token;

            // Test filtering by region
            const response = await request(server)
                .get('/api/esim-plans/partner?region=Europe')
                .set('Authorization', `Bearer ${token}`);

            expect(response.status).toBe(200);
            expect(response.body.plans).toHaveLength(1);
            expect(response.body.plans[0].name).toBe('Regional Plan');
        });

        it('should return empty array when no plans match filters', async () => {
            // Create test partner with hashed password
            const hashedPassword = await bcrypt.hash('password123', 10);
            const partner = await User.create({
                email: '<EMAIL>',
                password: hashedPassword,
                role: 'partner',
                isActive: true
            });

            // Get auth token
            const authResponse = await request(server)
                .post('/api/auth/login')
                .send({
                    email: '<EMAIL>',
                    password: 'password123'
                });

            expect(authResponse.status).toBe(200);
            const token = authResponse.body.token;

            // Test with non-existent region
            const response = await request(server)
                .get('/api/esim-plans/partner?region=NonExistent')
                .set('Authorization', `Bearer ${token}`);

            expect(response.status).toBe(200);
            expect(response.body.plans).toHaveLength(0);
        });
    });

    describe('GET /api/esim-plans/partner/:id', () => {
        it('should return a single plan details with all fields', async () => {
            // Create test data for plan details
            const testCountry = await Country.create({
                name: 'Test Country',
                flagUrl: 'http://example.com/flag.png',
                flagEmoji: '🏳️'
            });

            const testProvider = await Provider.create({
                name: 'Test Provider',
                type: 'Custom',
                status: 'active'
            });

            // Create a comprehensive test plan
            const testPlan = await EsimPlan.create({
                name: 'Complete Test Plan',
                productId: 'DETAILED123',
                description: 'Complete plan description for details view',
                instructions: 'Detailed installation instructions',
                features: JSON.stringify(['5G Support', 'Unlimited Calls', 'Roaming']),
                planInfo: 'Comprehensive plan information',
                planType: 'Fixed',
                planData: '20',
                planDataUnit: 'GB',
                validityDays: 30,
                buyingPrice: 15.00,
                sellingPrice: 25.00,
                status: 'visible',
                networkName: 'Test Network',
                networkType: '5G/LTE',
                region: 'Europe',
                category: 'esim_realtime',
                voiceMin: 100,
                sms: 200,
                speed: 'Unrestricted',
                profile: 'local',
                activationPolicy: 'Activation upon first use',
                startDateEnabled: true,
                isVoiceAvailable: true,
                isSmsAvailable: true,
                hotspotAvailable: true,
                topUpAvailable: true,
                providerId: testProvider.id
            });

            await testPlan.addCountry(testCountry);

            // Create test partner
            const hashedPassword = await bcrypt.hash('password123', 10);
            const partner = await User.create({
                email: '<EMAIL>',
                password: hashedPassword,
                role: 'partner',
                isActive: true,
                markupPercentage: 20
            });

            // Get auth token
            const authResponse = await request(server)
                .post('/api/auth/login')
                .send({
                    email: '<EMAIL>',
                    password: 'password123'
                });

            const token = authResponse.body.token;

            // Test the plan details endpoint
            const response = await request(server)
                .get(`/api/esim-plans/partner/${testPlan.id}`)
                .set('Authorization', `Bearer ${token}`);

            expect(response.status).toBe(200);
            
            // Verify all fields are present in plan details
            expect(response.body).toHaveProperty('id', testPlan.id);
            expect(response.body).toHaveProperty('productId', 'DETAILED123');
            expect(response.body).toHaveProperty('name', 'Complete Test Plan');
            expect(response.body).toHaveProperty('description', 'Complete plan description for details view');
            expect(response.body).toHaveProperty('instructions', 'Detailed installation instructions');
            expect(response.body).toHaveProperty('planInfo', 'Comprehensive plan information');
            expect(response.body).toHaveProperty('features');
            expect(response.body).toHaveProperty('networkName', 'Test Network');
            expect(response.body).toHaveProperty('networkType', '5G/LTE');
            expect(response.body).toHaveProperty('buyingPrice');
            expect(response.body).toHaveProperty('sellingPrice');
            expect(response.body).toHaveProperty('validityDays', 30);
            expect(response.body).toHaveProperty('planData', '20');
            expect(response.body).toHaveProperty('planDataUnit', 'GB');
            expect(response.body).toHaveProperty('planType', 'Fixed');
            expect(response.body).toHaveProperty('region', 'Europe');
            expect(response.body).toHaveProperty('category', 'esim_realtime');
            expect(response.body).toHaveProperty('voiceMin', 100);
            expect(response.body).toHaveProperty('sms', 200);
            expect(response.body).toHaveProperty('speed', 'Unrestricted');
            expect(response.body).toHaveProperty('profile', 'local');
            expect(response.body).toHaveProperty('activationPolicy', 'Activation upon first use');
            expect(response.body).toHaveProperty('startDateEnabled', true);
            expect(response.body).toHaveProperty('isVoiceAvailable', true);
            expect(response.body).toHaveProperty('isSmsAvailable', true);
            expect(response.body).toHaveProperty('hotspotAvailable', true);
            expect(response.body).toHaveProperty('topUpAvailable', true);
            
            // Verify provider and countries are included
            expect(response.body).toHaveProperty('provider');
            expect(response.body.provider).toHaveProperty('name', 'Test Provider');
            expect(response.body).toHaveProperty('countries');
            expect(response.body.countries).toHaveLength(1);
            expect(response.body.countries[0]).toHaveProperty('name', 'Test Country');
        });

        it('should return 404 for non-existent plan', async () => {
            // Create test partner
            const hashedPassword = await bcrypt.hash('password123', 10);
            const partner = await User.create({
                email: '<EMAIL>',
                password: hashedPassword,
                role: 'partner',
                isActive: true
            });

            // Get auth token
            const authResponse = await request(server)
                .post('/api/auth/login')
                .send({
                    email: '<EMAIL>',
                    password: 'password123'
                });

            const token = authResponse.body.token;

            // Test with non-existent plan ID
            const response = await request(server)
                .get('/api/esim-plans/partner/999999')
                .set('Authorization', `Bearer ${token}`);

            expect(response.status).toBe(404);
            expect(response.body).toHaveProperty('message');
        });

        it('should not return hidden plans in details view', async () => {
            // Create test data
            const testCountry = await Country.create({
                name: 'Test Country',
                flagUrl: 'http://example.com/flag.png',
                flagEmoji: '🏳️'
            });

            const hiddenPlan = await EsimPlan.create({
                name: 'Hidden Plan',
                productId: 'HIDDEN123',
                description: 'This plan is hidden',
                planType: 'Fixed',
                planData: '5',
                planDataUnit: 'GB',
                validityDays: 15,
                buyingPrice: 8.00,
                sellingPrice: 12.00,
                status: 'hidden',  // Hidden plan
                networkName: 'Test Network'
            });

            await hiddenPlan.addCountry(testCountry);

            // Create test partner
            const hashedPassword = await bcrypt.hash('password123', 10);
            const partner = await User.create({
                email: '<EMAIL>',
                password: hashedPassword,
                role: 'partner',
                isActive: true
            });

            // Get auth token
            const authResponse = await request(server)
                .post('/api/auth/login')
                .send({
                    email: '<EMAIL>',
                    password: 'password123'
                });

            const token = authResponse.body.token;

            // Try to access hidden plan details
            const response = await request(server)
                .get(`/api/esim-plans/partner/${hiddenPlan.id}`)
                .set('Authorization', `Bearer ${token}`);

            expect(response.status).toBe(404);
        });
    });

    describe('Provider Visibility Tests', () => {
        let adminUser, adminToken, testProvider, testCountry;

        beforeEach(async () => {
            // Create admin user
            const hashedPassword = await bcrypt.hash('admin123', 10);
            adminUser = await User.create({
                email: '<EMAIL>',
                password: hashedPassword,
                role: 'admin',
                isActive: true
            });

            // Get admin token
            const authResponse = await request(server)
                .post('/api/auth/login')
                .send({
                    email: '<EMAIL>',
                    password: 'admin123'
                });

            adminToken = authResponse.body.token;

            // Create test provider
            testProvider = await Provider.create({
                name: 'Test Provider',
                type: 'Custom',
                status: 'active'
            });

            // Create test country
            testCountry = await Country.create({
                name: 'Test Country',
                flagUrl: 'http://example.com/flag.png',
                flagEmoji: '🏳️'
            });
        });

        it('should create new plan as visible when provider has no hidden plans', async () => {
            // Create a plan for the provider (should be visible by default)
            const response = await request(server)
                .post('/api/esim-plans')
                .set('Authorization', `Bearer ${adminToken}`)
                .send({
                    name: 'Test Plan',
                    description: 'Test Description',
                    providerId: testProvider.id,
                    networkName: 'Test Network',
                    networkType: '4G/LTE',
                    buyingPrice: 10.00,
                    sellingPrice: 15.00,
                    validityDays: 30,
                    planType: 'Fixed',
                    planData: '10',
                    planDataUnit: 'GB',
                    category: 'esim_realtime',
                    planCategory: 'Data Only',
                    countries: [testCountry.id]
                });

            expect(response.status).toBe(201);
            expect(response.body.status).toBe('visible');
        });

        it('should create new plan as hidden when provider plans are hidden', async () => {
            // First create a plan and hide all provider plans
            const firstPlan = await EsimPlan.create({
                name: 'First Plan',
                productId: 'FIRST123',
                providerId: testProvider.id,
                networkName: 'Test Network',
                status: 'visible'
            });

            // Hide all plans for this provider
            await request(server)
                .put(`/api/esim-plans/provider/${testProvider.id}/visibility`)
                .set('Authorization', `Bearer ${adminToken}`)
                .send({ status: 'hidden' });

            // Now create a new plan - it should be hidden
            const response = await request(server)
                .post('/api/esim-plans')
                .set('Authorization', `Bearer ${adminToken}`)
                .send({
                    name: 'New Plan',
                    description: 'New Plan Description',
                    providerId: testProvider.id,
                    networkName: 'Test Network',
                    networkType: '4G/LTE',
                    buyingPrice: 10.00,
                    sellingPrice: 15.00,
                    validityDays: 30,
                    planType: 'Fixed',
                    planData: '10',
                    planDataUnit: 'GB',
                    category: 'esim_realtime',
                    planCategory: 'Data Only',
                    countries: [testCountry.id]
                });

            expect(response.status).toBe(201);
            expect(response.body.status).toBe('hidden');
        });

        it('should create bulk plans as hidden when provider plans are hidden', async () => {
            // First create a plan and hide all provider plans
            const firstPlan = await EsimPlan.create({
                name: 'First Plan',
                productId: 'FIRST123',
                providerId: testProvider.id,
                networkName: 'Test Network',
                status: 'visible'
            });

            // Hide all plans for this provider
            await request(server)
                .put(`/api/esim-plans/provider/${testProvider.id}/visibility`)
                .set('Authorization', `Bearer ${adminToken}`)
                .send({ status: 'hidden' });

            // Now create bulk plans - they should be hidden
            const bulkPlansData = [
                {
                    name: 'Bulk Plan 1',
                    description: 'Bulk Plan 1 Description',
                    providerId: testProvider.id,
                    networkName: 'Test Network',
                    networkType: '4G/LTE',
                    buyingPrice: 10.00,
                    sellingPrice: 15.00,
                    validityDays: 30,
                    planType: 'Fixed',
                    planData: '10',
                    planDataUnit: 'GB',
                    category: 'esim_realtime',
                    planCategory: 'Data Only',
                    countries: [testCountry.id]
                },
                {
                    name: 'Bulk Plan 2',
                    description: 'Bulk Plan 2 Description',
                    providerId: testProvider.id,
                    networkName: 'Test Network',
                    networkType: '4G/LTE',
                    buyingPrice: 12.00,
                    sellingPrice: 18.00,
                    validityDays: 30,
                    planType: 'Fixed',
                    planData: '20',
                    planDataUnit: 'GB',
                    category: 'esim_realtime',
                    planCategory: 'Data Only',
                    countries: [testCountry.id]
                }
            ];

            const response = await request(server)
                .post('/api/esim-plans/bulk')
                .set('Authorization', `Bearer ${adminToken}`)
                .send(bulkPlansData);

            expect(response.status).toBe(201);
            expect(response.body.plans).toHaveLength(2);
            expect(response.body.plans[0].status).toBe('hidden');
            expect(response.body.plans[1].status).toBe('hidden');
        });
    });
});
