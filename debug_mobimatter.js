require('dotenv').config({ path: './server/.env' });

const mobimatterService = require('./server/src/services/mobimatter.service');

async function debugAPI() {
  try {
    console.log('Fetching Mobimatter products...');
    const products = await mobimatterService.getProducts();
    console.log(`Found ${products.length} products`);
    
    if (products.length > 0) {
      const sample = products[0];
      console.log('\n=== Sample Product ===');
      console.log('ID:', sample.id);
      console.log('Name:', sample.name);
      console.log('supportedCountries:', sample.supportedCountries);
      console.log('supportedCountries type:', typeof sample.supportedCountries);
      console.log('supportedCountries is array:', Array.isArray(sample.supportedCountries));
      console.log('supportedCountries length:', sample.supportedCountries?.length);
      
      if (sample.providerMetadata) {
        console.log('\n=== Provider Metadata ===');
        console.log('Keys:', Object.keys(sample.providerMetadata));
        if (sample.providerMetadata.countries) {
          console.log('providerMetadata.countries:', sample.providerMetadata.countries);
          console.log('providerMetadata.countries type:', typeof sample.providerMetadata.countries);
        }
        if (sample.providerMetadata.originalRegions) {
          console.log('providerMetadata.originalRegions:', sample.providerMetadata.originalRegions);
        }
      }
    }
  } catch (error) {
    console.error('Error:', error.message);
    console.error('Stack:', error.stack);
  }
}

debugAPI();
