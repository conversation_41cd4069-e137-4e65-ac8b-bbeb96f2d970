import { useState, useEffect, useRef, useMemo, useCallback, memo } from 'react';
import PropTypes from 'prop-types';
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { Wifi, Globe, Search, Loader2, X, MapPin, Shield, ChevronLeft, ChevronRight } from 'lucide-react';
import api from '@/lib/axios';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { io } from 'socket.io-client';
import { useAuth } from '@/contexts/AuthContext';

// Memoize color scheme calculations
const getColorScheme = (planType) => {
    switch (planType) {
      case 'Unlimited': return { bg: 'bg-blue-700', light: 'bg-blue-50', text: 'text-blue-700' };
      case 'Custom': return { bg: 'bg-blue-400', light: 'bg-blue-50', text: 'text-blue-600' };
      case 'Fixed': return { bg: 'bg-purple-500', light: 'bg-purple-50', text: 'text-purple-600' };
      default: return { bg: 'bg-gray-800', light: 'bg-gray-50', text: 'text-gray-600' };
    }
  };

// Memoize the PlanCard component
const PlanCard = memo(({ plan, onAddToCart, navigate }) => {
    // Memoize color scheme
    const colors = useMemo(() => getColorScheme(plan.planType), [plan.planType]);

    // Memoize data plan formatting
    const formattedDataPlan = useMemo(() => {
    if (plan.planType === 'Unlimited') return 'Unlimited Data';
    if (plan.planType === 'Custom') return plan.customPlanData;
    if (plan.planType === 'Fixed') return `${plan.planData} ${plan.planDataUnit}`;
    return 'Data plan not specified';
    }, [plan.planType, plan.customPlanData, plan.planData, plan.planDataUnit]);

    // Memoize regions processing
    const regions = useMemo(() => {
        return typeof plan.region === 'string'
    ? plan.region.split(',').map(r => r.trim())
    : Array.isArray(plan.region)
      ? plan.region
      : [];
    }, [plan.region]);

    // Memoize price processing
    const { displayPrice, originalPrice } = useMemo(() => ({
        displayPrice: typeof plan.displayPrice === 'string' ? parseFloat(plan.displayPrice) : plan.displayPrice,
        originalPrice: plan.originalPrice ? (typeof plan.originalPrice === 'string' ? parseFloat(plan.originalPrice) : plan.originalPrice) : null
    }), [plan.displayPrice, plan.originalPrice]);

    // Memoize supported countries
    const supportedCountries = useMemo(() => {
        // Primary: Use countries field from database association
        if (plan.countries && plan.countries.length > 0) {
            return plan.countries;
        }
        
        // Fallback for Mobimatter plans: Extract from provider metadata if database association is missing
        if (plan.provider?.name === 'Mobimatter' && plan.providerMetadata?.originalData?.supportedCountries) {
            const countryCodes = plan.providerMetadata.originalData.supportedCountries;
            // Convert country codes to objects with id and name for consistent display
            return countryCodes.map(code => ({
                id: code,
                name: code, // Use code as name for now, could be enhanced with country name mapping
                flagUrl: `https://flagcdn.com/w20/${code.toLowerCase()}.png`
            }));
        }
        
        // Final fallback: empty array
        return [];
    }, [plan.countries, plan.provider?.name, plan.providerMetadata?.originalData?.supportedCountries]);

    // Memoize and normalize features
    const normalizedFeatures = useMemo(() => {
        if (!plan.features) return [];
        if (Array.isArray(plan.features)) return plan.features;
        if (typeof plan.features === 'object') {
            // Convert object to array if it's an object
            return Object.values(plan.features).filter(feature => feature && typeof feature === 'string');
        }
        return [];
    }, [plan.features]);

    // Handle view details click
    const handleViewDetails = useCallback(() => {
        const planId = plan.id || plan.productId || plan.externalProductId;
        if (planId) {
            navigate(`/dashboard/plans/${planId}`);
        } else {
            console.warn('Plan has no valid ID for navigation:', plan);
        }
    }, [navigate, plan.id, plan.productId, plan.externalProductId, plan]);

  return (
    <Card className="flex flex-col h-full transition-all hover:shadow-lg border border-gray-100 hover:border-blue-200 rounded-xl overflow-hidden group relative">
      <div className={`h-2 w-full ${colors.bg}`} />

      <CardHeader className="pb-2">
        <div className="flex items-start justify-between">
          <div>
            <Badge variant="outline" className={`mb-2 ${colors.light} ${colors.text} border-0`}>
              {plan.planType}
            </Badge>
            <Badge variant="outline" className={`mb-2 ${colors.light} ${colors.text} border-0`}>
              {plan.planCategory}
            </Badge>
            <CardTitle className="text-xl text-gray-800 font-bold">{plan.name}</CardTitle>
            {/* <p className="text-sm text-gray-500 mt-1">{plan.networkName}</p> */}
          </div>
          <div className={`p-2 rounded-lg ${colors.light} transition-all group-hover:scale-110`}>
            <Globe className={`w-6 h-6 ${colors.text}`} />
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col pt-0">
        <div className="flex items-baseline mb-6">
          <span className="text-3xl font-bold">${displayPrice}</span>
          <span className="text-gray-500 ml-2">/ {plan.validityDays} Day{plan.validityDays > 1 ? 's' : ''}</span>
          {originalPrice && originalPrice > displayPrice && (
            <span className="ml-2 line-through text-gray-400 text-sm">${originalPrice}</span>
          )}
        </div>

        <div className="space-y-4 mb-6">
          <div className="p-3 rounded-lg bg-gray-50 flex items-center gap-3 group-hover:shadow-sm transition-all">
            <div className={`p-2 rounded-full ${colors.light}`}>
              <Wifi className={`w-4 h-4 ${colors.text}`} />
            </div>
            <div>
              <span className="text-sm text-gray-700">Data Plan</span>
              <p className={`font-medium ${colors.text}`}>
                                {formattedDataPlan}
              </p>
            </div>
          </div>

          <div className="flex flex-col gap-4">


            <div className="flex items-start gap-3">
              <div className="bg-gray-100 p-1.5 rounded-full">
                <MapPin className="w-4 h-4 text-gray-600" />
              </div>
              <div>
                <span className="text-sm">Available in</span>
                <div className="mt-1 flex flex-wrap gap-2">
                                    {supportedCountries.slice(0, 2).map((country, index) => {
                    // Handle both object and string formats
                    const countryCode = typeof country === 'string' ? country : country.id;
                    const countryName = typeof country === 'string' ? country : country.name;
                    // For local plans, use the country.id directly, for Mobimatter use the country code
                    const flagUrl = typeof country === 'string'
                      ? `https://flagcdn.com/w20/${country.toLowerCase()}.png`
                      : country.flagUrl || `https://flagcdn.com/w20/${countryCode.toLowerCase()}.png`;

                    return (
                      <div
                        key={`plan-${plan.id}-country-${countryCode}-idx-${index}`}
                        className="inline-flex items-center gap-1 bg-gray-50 px-2 py-1 rounded-md hover:bg-gray-100 transition-all cursor-pointer"
                      >
                        <img
                          src={flagUrl}
                          alt={`${countryName} flag`}
                        className="w-4 h-3 object-cover rounded-sm"
                          title={countryName}
                          onError={(e) => {
                            e.target.onerror = null;
                            e.target.src = 'https://upload.wikimedia.org/wikipedia/commons/thumb/b/b0/No_flag.svg/32px-No_flag.svg.png';
                          }}
                        />
                        <span className="text-xs font-medium">{countryName}</span>
                    </div>
                    );
                  })}
                                    {supportedCountries.length > 2 && (
                    <span className="text-xs bg-gray-50 px-2 py-1 rounded-md text-gray-500 hover:bg-gray-100 transition-all cursor-pointer">
                                            +{supportedCountries.length - 2} more
                    </span>
                  )}
                </div>
              </div>
            </div>

                        {normalizedFeatures.length > 0 && (
              <div className="flex items-start gap-3">
                <div className="bg-gray-100 p-1.5 rounded-full">
                  <Shield className="w-4 h-4 text-gray-600" />
                </div>
                <div>
                  <span className="text-sm">Features</span>
                  <div className="mt-1 flex flex-wrap gap-1">
                                        {normalizedFeatures.slice(0, 2).map((feature, index) => (
                      <span key={index} className={`text-xs ${colors.light} ${colors.text} px-2 py-0.5 rounded-full`}>
                        {feature}
                      </span>
                    ))}
                                        {normalizedFeatures.length > 2 && (
                                            <span className="text-xs text-gray-500 px-2 py-0.5 rounded-full bg-gray-50">+{normalizedFeatures.length - 2} more</span>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="mt-auto grid grid-cols-1 gap-2">
          <Button
                        className={`${colors.bg} hover:bg-opacity-90 transition-all`}
                        onClick={handleViewDetails}
          >
            View Details
          </Button>
        </div>

        {/* Out of Stock Badge - only shown when stock is 0 and not an API provider plan */}
        {plan.stockCount === 0 &&
         plan.provider?.name !== 'Mobimatter' &&
         plan.provider?.name !== 'Billionconnect' &&
         plan.provider?.type !== 'API' &&
         plan.stockCount !== 'Unlimited' && (
          <div className="absolute top-2 right-2">
            <Badge variant="destructive" className="px-2 py-1">
              Out of stock
            </Badge>
          </div>
        )}
      </CardContent>
    </Card>
  );
}, (prevProps, nextProps) => {
    // Custom comparison function for memo
    return (
        prevProps.plan.id === nextProps.plan.id &&
        prevProps.plan.displayPrice === nextProps.plan.displayPrice &&
        prevProps.plan.planType === nextProps.plan.planType &&
        prevProps.plan.region === nextProps.plan.region &&
        JSON.stringify(prevProps.plan.features) === JSON.stringify(nextProps.plan.features)
    );
});

// Add display name for the component
PlanCard.displayName = 'PlanCard';

// Update PropTypes to allow for both array and object
PlanCard.propTypes = {
  plan: PropTypes.shape({
    planType: PropTypes.string,
    customPlanData: PropTypes.string,
    planData: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number
    ]),
    planDataUnit: PropTypes.string,
    planCategory: PropTypes.string,
    name: PropTypes.string.isRequired,
    networkName: PropTypes.string,
    displayPrice: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number
    ]).isRequired,
    validityDays: PropTypes.number.isRequired,
    originalPrice: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number
    ]),
    region: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.arrayOf(PropTypes.string)
    ]),
    countries: PropTypes.arrayOf(PropTypes.shape({
      id: PropTypes.string.isRequired,
      name: PropTypes.string.isRequired,
      flagUrl: PropTypes.string, 
    })),
    mobimatterCountries: PropTypes.arrayOf(PropTypes.shape({
      isoName: PropTypes.string.isRequired,
      name: PropTypes.string.isRequired,
    })),
        features: PropTypes.oneOfType([
            PropTypes.arrayOf(PropTypes.string),
            PropTypes.object
        ]),
    stockCount: PropTypes.number,
    provider: PropTypes.shape({
      name: PropTypes.string.isRequired,
    }),
    id: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number
    ]),
    productId: PropTypes.string,
    externalProductId: PropTypes.string,
    providerMetadata: PropTypes.shape({
      originalData: PropTypes.shape({
        supportedCountries: PropTypes.arrayOf(PropTypes.string)
      })
    }),
  }).isRequired,
  onAddToCart: PropTypes.func.isRequired,
  navigate: PropTypes.func.isRequired,
};

// The main Plans component 
export default function Plans() {
    const { user } = useAuth();
    const [searchParams, setSearchParams] = useSearchParams();
    const [loading, setLoading] = useState(false);
    const [filteredPlans, setFilteredPlans] = useState([]);
    const [countries, setCountries] = useState(() => {
        const cached = sessionStorage.getItem('countries');
        return cached ? JSON.parse(cached) : [];
    });
    const [regions, setRegions] = useState(() => {
        const cached = sessionStorage.getItem('regions');
        return cached ? JSON.parse(cached) : [];
    });
    const [selectedCountry, setSelectedCountry] = useState(searchParams.get('country') || 'all');
    const [selectedRegion, setSelectedRegion] = useState(searchParams.get('region') || 'all');
    const [currentTab, setCurrentTab] = useState(searchParams.get('tab') || 'esim_realtime');
    const [searchQuery, setSearchQuery] = useState(searchParams.get('search') || '');
    const [currentPage, setCurrentPage] = useState(parseInt(searchParams.get('page') || '1'));
    const [totalPages, setTotalPages] = useState(1);
    const [returningFromDetails, setReturningFromDetails] = useState(false);
    const { toast } = useToast();
    const navigate = useNavigate();
    const isInitialMount = useRef(true);
    const searchTimeout = useRef(null);
    const filterTimeout = useRef(null);
    const isUserInitiatedSearch = useRef(false);

    // Optimize cache management with size limit and expiration
    const CACHE_EXPIRY = 30 * 60 * 1000; // 30 minutes for regular queries
    const SEARCH_CACHE_EXPIRY = 15 * 60 * 1000; // 15 minutes for search results

    const cleanupCache = useCallback(() => {
        try {
            const keys = Object.keys(sessionStorage);

            // Step 1: Identify all plans cache items and sort by timestamp (oldest first)
            const plansCacheKeys = keys.filter(key => key.startsWith('plans_'))
                .sort((a, b) => {
                    try {
                        const aData = JSON.parse(sessionStorage.getItem(a) || '{}');
                        const bData = JSON.parse(sessionStorage.getItem(b) || '{}');
                        return (aData.timestamp || 0) - (bData.timestamp || 0);
                    } catch (e) {
                        return 0;
                    }
                });

            // Remove oldest 1/3 of the plans cache entries
            if (plansCacheKeys.length > 3) {
                const itemsToRemove = Math.floor(plansCacheKeys.length / 3);
                plansCacheKeys.slice(0, itemsToRemove).forEach(key => {
                    try {
                        sessionStorage.removeItem(key);
                    } catch (e) {
                        console.warn(`Failed to remove plans cache during cleanup: ${key}`, e);
                    }
                });
            }

            // Step 2: Remove expired search query caches (older than 5 minutes)
            keys.filter(key => key.startsWith('plans_') && key.includes('search'))
                .forEach(key => {
                try {
                    const data = JSON.parse(sessionStorage.getItem(key) || '{}');
                        if (data.timestamp && (Date.now() - data.timestamp > 5 * 60 * 1000)) {
                            sessionStorage.removeItem(key);
                    }
                } catch (e) {
                        // Remove if can't parse
                try {
                    sessionStorage.removeItem(key);
                        } catch (removeError) {
                            // Ignore
                        }
                    }
                });

            // Step 3: Preserve critical items and clean up the rest as needed
            const criticalKeys = [
                'lastPlansResponse_esim_realtime',
                'lastPlansResponse_physical_sim',
                'filteredPlans',
                'countries',
                'regions'
            ];

            const nonCriticalKeys = keys.filter(key => !criticalKeys.includes(key) &&
                                                         !key.endsWith('_esim_realtime') &&
                                                         !key.endsWith('_physical_sim'));

            if (nonCriticalKeys.length > 50) {
                nonCriticalKeys.slice(0, 20).forEach(key => {
                    try {
                        sessionStorage.removeItem(key);
                        } catch (e) {
                        // Ignore errors during desperate cleanup
                        }
                    });
            }
        } catch (e) {
            // console.warn('Error during cache cleanup:', e);
        }
    }, []);

    const ensureStorageSpace = useCallback(() => {
        try {
            let totalSize = 0;
            let largestItems = [];

            Object.keys(sessionStorage).forEach(key => {
                try {
                    const value = sessionStorage.getItem(key) || '';
                    const size = value.length * 2; 
                    totalSize += size;

                    largestItems.push({ key, size });
                    } catch (e) {
                    // Skip items we can't access
                }
            });

            largestItems.sort((a, b) => b.size - a.size);

            const estimatedQuotaUsage = totalSize / (5 * 1024 * 1024); // Percentage of typical 5MB quota
            if (estimatedQuotaUsage > 0.75) {
                cleanupCache();

                // If we have very large items (>500KB), consider removing them
                const veryLargeItems = largestItems.filter(item =>
                    item.size > 500 * 1024 &&
                    !item.key.includes('lastPlansResponse') &&
                    !item.key.includes('filteredPlans')
                );

                if (veryLargeItems.length > 0) {
                    veryLargeItems.slice(0, 3).forEach(item => {
                    try {
                        sessionStorage.removeItem(item.key);
                    } catch (e) {
                            // Ignore errors
                        }
                    });
                }
            }

            try {
                const testKey = `test_space_${Date.now()}`;
                sessionStorage.setItem(testKey, '1');
                sessionStorage.removeItem(testKey);
                    } catch (e) {
                // Remove the 5 largest items that aren't critical
                largestItems
                    .filter(item => !item.key.includes('lastPlansResponse') && !item.key.includes('filteredPlans'))
                    .slice(0, 5)
                    .forEach(item => {
                    try {
                        sessionStorage.removeItem(item.key);
                        } catch (removeError) {
                            // Ignore
                    }
                });
            }

        } catch (e) {
            console.warn('Error ensuring storage space:', e);
            try {
                Object.keys(sessionStorage).forEach(key => {
                    if (!key.includes('lastPlansResponse') && !key.includes('filteredPlans') &&
                        key !== 'countries' && key !== 'regions') {
                        sessionStorage.removeItem(key);
                    }
                });
            } catch (finalError) {
                console.error('Critical failure in storage management:', finalError);
            }
        }
    }, [cleanupCache]);

    const fetchPlans = useCallback(async (pageNum = 1) => {
        try {
            const isSearchQuery = (searchQuery || '').trim().length > 0;

            // Don't show loading indicator for cached results or quick refreshes
            let shouldShowLoading = true;

            const params = {
                    countryId: selectedCountry === 'all' ? '' : selectedCountry,
                    region: selectedRegion === 'all' ? '' : selectedRegion,
                    category: currentTab,
                    page: pageNum,
                    limit: 24,
                search: (searchQuery || '').toString().trim() || undefined
            };

            const cacheKey = `plans_${JSON.stringify(params)}`;

            const filtersCleared = sessionStorage.getItem('filtersCleared');
            if (filtersCleared === 'true') {
                sessionStorage.removeItem('filtersCleared');
                setReturningFromDetails(false);
                shouldShowLoading = true;
            }
            else if (returningFromDetails) {
                console.log('fetchPlans: Returning from details, checking cache...');
                try {
                    const lastResponseData = sessionStorage.getItem(`lastPlansResponse_${currentTab}`);

                    console.log('fetchPlans cache check:', {
                        hasData: !!lastResponseData,
                        currentTab,
                        returningFromDetails
                    });

                    if (lastResponseData) {
                        const parsed = JSON.parse(lastResponseData);
                        console.log('fetchPlans parsed data:', {
                            hasPlans: !!parsed.plans,
                            plansLength: parsed.plans?.length,
                            timestamp: parsed.timestamp
                        });
                        if (parsed && parsed.plans && parsed.timestamp && Array.isArray(parsed.plans) && parsed.plans.length > 0) {
                            const plansCount = parsed.plans.length;
                            const totalPages = parsed.totalPages || 1;
                            const expectedPages = Math.ceil(plansCount / 24);

                            // If cached totalPages is significantly different from expected, invalidate cache
                            if (totalPages > expectedPages * 2) {
                                console.log('Returning from details - cache invalidated due to suspicious pagination:', {
                                    plansCount,
                                    cachedTotalPages: totalPages,
                                    expectedPages
                                });
                                sessionStorage.removeItem(lastResponseKey);
                                sessionStorage.removeItem('filteredPlans');
                                setReturningFromDetails(false);
                            } else {
                                // Use cache if it's less than 15 minutes old
                                if (Date.now() - parsed.timestamp < 15 * 60 * 1000) {
                                    shouldShowLoading = false;
                                    setFilteredPlans(parsed.plans);
                                    setTotalPages(parsed.totalPages || 1);
                                    setCurrentPage(pageNum);
                                    if (parsed.countries && parsed.countries.length > 0) {
                                        setCountries(parsed.countries);
                                    }
                                    if (parsed.regions && parsed.regions.length > 0) {
                                        setRegions(parsed.regions);
                                    }

                                    // Reset the returning state after a short delay to ensure UI updates
                                    setTimeout(() => {
                                        setReturningFromDetails(false);
                                    }, 100);

                                    setTimeout(() => {
                                        api.get('/api/esim-plans/partner', { params })
                                            .then(response => {
                                                if (response.data.plans && Array.isArray(response.data.plans)) {
                                                    setFilteredPlans(response.data.plans);
                                                    setTotalPages(response.data.totalPages);

                                                    const freshData = {
                                                        plans: response.data.plans,
                                                        totalPages: response.data.totalPages,
                                                        countries: response.data.countries,
                                                        regions: response.data.regions,
                                                        timestamp: Date.now()
                                                    };

                                                    try {
                                                        sessionStorage.setItem(cacheKey, JSON.stringify(freshData));
                                                        sessionStorage.setItem(`lastPlansResponse_${currentTab}`, JSON.stringify(freshData));
                                                    } catch (e) {
                                                        console.warn('Error updating cache:', e);
                                                    }
                                                }
                                            })
                                            .catch(err => {
                                                console.warn('Background refresh failed:', err);
                                            });
                                    }, 1000);

                                    return;
                                }
                            }
                        }
                    }
                } catch (e) {
                    console.warn('Error accessing cached response:', e);
                }

                setReturningFromDetails(false);
            }

            let cachedData = null;
            try {
                cachedData = sessionStorage.getItem(cacheKey);
            } catch (e) {
                console.warn('Error accessing sessionStorage:', e);
                cleanupCache();
            }

            if (cachedData) {
                try {
                    const parsed = JSON.parse(cachedData);
                    const maxAge = isSearchQuery ? SEARCH_CACHE_EXPIRY : CACHE_EXPIRY;

                    if (Date.now() - parsed.timestamp < maxAge) {
                        const plansCount = parsed.plans?.length || 0;
                        const totalPages = parsed.totalPages || 1;
                        const totalItems = parsed.totalItems || 0;

                        const hasValidStructure = Array.isArray(parsed.plans) &&
                                                typeof totalPages === 'number' &&
                                                totalPages > 0;

                        const expectedTotalPages = Math.ceil(totalItems / 24);
                        const paginationIsValid = totalItems === 0 || Math.abs(totalPages - expectedTotalPages) <= 1;

                        if (!hasValidStructure || !paginationIsValid) {
                            console.log('Cache invalidated due to invalid data structure:', {
                                plansCount,
                                totalPages,
                                totalItems,
                                expectedTotalPages,
                                hasValidStructure,
                                paginationIsValid
                            });
                            sessionStorage.removeItem(cacheKey);
                        } else {
                            shouldShowLoading = false;
                            setFilteredPlans(parsed.plans);
                            setTotalPages(parsed.totalPages);
                            setCurrentPage(pageNum);
                            if (parsed.countries && parsed.countries.length > 0) {
                                setCountries(parsed.countries);
                            }
                            if (parsed.regions && parsed.regions.length > 0) {
                                setRegions(parsed.regions);
                            }

                            if (isSearchQuery || pageNum > 1) {
                                return;
                            }
                        }
                    }
                    try {
                        sessionStorage.removeItem(cacheKey);
                    } catch (e) {
                        console.warn('Error removing expired cache:', e);
                    }
                } catch (e) {
                    // Invalid cache, remove it
                    try {
                        sessionStorage.removeItem(cacheKey);
                    } catch (storageError) {
                        console.warn('Error removing corrupted cache:', storageError);
                    }
                }
            }

            if (shouldShowLoading) {
                setLoading(true);
            }

            // Fetch new data
            const response = await api.get('/api/esim-plans/partner', { params });

            const responseData = {
                plans: response.data.plans,
                totalPages: response.data.totalPages,
                countries: response.data.countries,
                regions: response.data.regions,
                timestamp: Date.now()
            };

            // Update state
            setFilteredPlans(responseData.plans);
            setTotalPages(responseData.totalPages);
            setCurrentPage(pageNum);
            if (responseData.countries) setCountries(responseData.countries);
            if (responseData.regions) setRegions(responseData.regions);

            ensureStorageSpace();

            try {
                sessionStorage.setItem(cacheKey, JSON.stringify(responseData));

                sessionStorage.setItem(`lastPlansResponse_${currentTab}`, JSON.stringify(responseData));

                sessionStorage.setItem('filteredPlans', JSON.stringify(responseData.plans));
            } catch (storageError) {
                console.warn('Error caching plans data:', storageError);

                cleanupCache();
                try {
                    const minimalData = {
                        plans: responseData.plans,
                        totalPages: responseData.totalPages,
                        timestamp: Date.now()
                    };
                    sessionStorage.setItem(cacheKey, JSON.stringify(minimalData));
                    sessionStorage.setItem(`lastPlansResponse_${currentTab}`, JSON.stringify(minimalData));
                    sessionStorage.setItem('filteredPlans', JSON.stringify(responseData.plans));
                } catch (e) {
                    console.error('Failed to cache data even after cleanup:', e);
                }
            }

        } catch (error) {
            console.error('Error fetching plans:', error);
            toast({
                variant: "destructive",
                title: "Error",
                description: "Failed to fetch plans. Please try again later.",
            });
        } finally {
            setLoading(false);
        }
    }, [selectedCountry, selectedRegion, currentTab, searchQuery, returningFromDetails, toast, ensureStorageSpace, cleanupCache]);

    const updateSearchParams = useCallback((updates) => {
        const newParams = new URLSearchParams(searchParams);

        // If we're explicitly setting search to empty, ensure we clear any search-related session storage
        if (updates.hasOwnProperty('search') && (!updates.search || updates.search === '')) {
            sessionStorage.removeItem('lastSearchQuery');
            newParams.delete('search');
        } else if (updates.hasOwnProperty('search') && updates.search) {
            newParams.set('search', updates.search.toString());
        }

        Object.entries(updates).forEach(([key, value]) => {
            if (key === 'search') {
                return;
            }

            if (value === 'all' || value === '' || !value ||
                (key === 'page' && (value === 1 || value === '1')) ||
                (key === 'tab' && value === 'esim_realtime')) {
                newParams.delete(key);
            } else {
                newParams.set(key, value.toString());
            }
        });

        setSearchParams(newParams);
    }, [searchParams, setSearchParams]);

    const handleFilterChange = useCallback((key, value) => {
        switch (key) {
            case 'country':
                setSelectedCountry(value);
                break;
            case 'region':
                setSelectedRegion(value);
                break;
            case 'tab':
                setCurrentTab(value);
                break;
            default:
                break;
        }
        setCurrentPage(1);

        if (filterTimeout.current) {
            clearTimeout(filterTimeout.current);
        }

        const newParams = {
            country: key === 'country' ? value : selectedCountry,
            region: key === 'region' ? value : selectedRegion,
            tab: key === 'tab' ? value : currentTab,
            page: 1,
            search: (searchQuery || '').trim()
        };

        updateSearchParams(newParams);

        const fetchParams = {
            countryId: newParams.country === 'all' ? '' : newParams.country,
            region: newParams.region === 'all' ? '' : newParams.region,
            category: newParams.tab,
            page: 1,
            limit: 24,
            search: newParams.search || undefined
        };

        setLoading(true);

        filterTimeout.current = setTimeout(() => {
            const cacheKey = `plans_${JSON.stringify(fetchParams)}`;
            const cachedData = sessionStorage.getItem(cacheKey);

            if (cachedData) {
                try {
                    const parsedData = JSON.parse(cachedData);
                    if (Date.now() - parsedData.timestamp < 300000) {
                        setFilteredPlans(parsedData.plans);
                        setTotalPages(parsedData.totalPages);
                        if (parsedData.countries) setCountries(parsedData.countries);
                        if (parsedData.regions) setRegions(parsedData.regions);
                        setLoading(false);
                        return;
                    }
                } catch (e) {
                    console.warn('Error parsing cached data:', e);
                }
            }

            // Fetch from API if no valid cache
            api.get('/api/esim-plans/partner', { params: fetchParams })
            .then(response => {
                const responseData = {
                    plans: response.data.plans,
                    totalPages: response.data.totalPages,
                    countries: response.data.countries,
                    regions: response.data.regions,
                    timestamp: Date.now()
                };

                setFilteredPlans(responseData.plans);
                setTotalPages(responseData.totalPages);
                if (responseData.countries) setCountries(responseData.countries);
                if (responseData.regions) setRegions(responseData.regions);

                // Debug pagination issues
                if (responseData.plans.length !== 24 && responseData.totalPages > 1) {
                    console.warn(`Pagination issue: Page ${fetchParams.page} returned ${responseData.plans.length} plans, expected 24. Total pages: ${responseData.totalPages}`);
                }

                try {
                    sessionStorage.setItem(`plans_${JSON.stringify(fetchParams)}`, JSON.stringify(responseData));
                } catch (e) {
                    console.warn('Error caching plans data:', e);
                }
            })
            .catch(error => {
                console.error('Error fetching plans:', error);
                toast({
                    variant: "destructive",
                    title: "Error",
                    description: "Failed to fetch plans. Please try again later.",
                });
            })
            .finally(() => {
                setLoading(false);
            });
        }, 200); // 200ms debounce delay for filters
    }, [selectedCountry, selectedRegion, currentTab, searchQuery, updateSearchParams, toast]);



    // Optimize component initial mount to reduce API load
    useEffect(() => {
        if (isInitialMount.current) {
            isInitialMount.current = false;

            const fromDetails = sessionStorage.getItem('fromPlanDetails');
            if (fromDetails === 'true') {
                sessionStorage.removeItem('fromPlanDetails');

                const wasClearedBeforeDetails = sessionStorage.getItem('filtersCleared');
                if (wasClearedBeforeDetails === 'true') {
                    sessionStorage.removeItem('filtersCleared');
                    sessionStorage.removeItem('lastSearchQuery');
                    sessionStorage.removeItem('lastDetailViewTime');
                    sessionStorage.removeItem('returningToDefaultView');

                    setSelectedCountry('all');
                    setSelectedRegion('all');
                    setSearchQuery('');
                    setCurrentPage(1);

                    setSearchParams({});

                    fetchPlans(1);
                    return;
                }

                setReturningFromDetails(true);

                const lastResponseKey = `lastPlansResponse_${currentTab}`;
                const lastResponseData = sessionStorage.getItem(lastResponseKey);

                console.log('Returning from details - checking cache:', {
                    lastResponseKey,
                    hasData: !!lastResponseData,
                    currentTab
                });

                if (lastResponseData) {
                    try {
                        const parsed = JSON.parse(lastResponseData);
                        console.log('Parsed cache data:', {
                            hasPlans: !!parsed.plans,
                            plansLength: parsed.plans?.length,
                            timestamp: parsed.timestamp
                        });
                        if (parsed && parsed.plans && Array.isArray(parsed.plans) && parsed.plans.length > 0) {
                            const plansCount = parsed.plans.length;
                            const totalPages = parsed.totalPages || 1;
                            const expectedPages = Math.ceil(plansCount / 24);

                            if (totalPages > expectedPages * 2) {
                                console.log('Cache invalidated due to suspicious pagination:', {
                                    plansCount,
                                    cachedTotalPages: totalPages,
                                    expectedPages
                                });
                                sessionStorage.removeItem(lastResponseKey);
                                sessionStorage.removeItem('filteredPlans');
                                setReturningFromDetails(false);
                                fetchPlans(1);
                                return;
                            }

                            setFilteredPlans(parsed.plans);
                            setTotalPages(parsed.totalPages || 1);
                            setCurrentPage(1);
                            if (parsed.countries && parsed.countries.length > 0) {
                                setCountries(parsed.countries);
                            }
                            if (parsed.regions && parsed.regions.length > 0) {
                                setRegions(parsed.regions);
                            }
                            setLoading(false);

                            setTimeout(() => {
                                setReturningFromDetails(false);
                            }, 100);

                            setTimeout(() => {
                                fetchPlans(1);
                            }, 1000);
                            return;
                        }
                    } catch (e) {
                        console.warn('Error parsing cached response:', e);
                    }
                }

                const cachedPlans = sessionStorage.getItem('filteredPlans');
                if (cachedPlans) {
                    try {
                        const parsed = JSON.parse(cachedPlans);
                        if (Array.isArray(parsed) && parsed.length > 0) {
                            setFilteredPlans(parsed);
                            setTotalPages(Math.ceil(parsed.length / 24));
                            setLoading(false);

                            setTimeout(() => {
                                setReturningFromDetails(false);
                            }, 100);

                            setTimeout(() => {
                                fetchPlans(1);
                            }, 1000);
                            return;
                        }
                    } catch (e) {
                        console.warn('Error parsing cached plans:', e);
                    }
                }

                setReturningFromDetails(false);
            }

            fetchPlans(1);
        }
    }, []); 

    const cleanupOldPlansCache = useCallback(() => {
        try {
            const keys = Object.keys(sessionStorage);

            const criticalKeys = [
                `defaultView_esim_realtime`,
                `defaultView_physical_sim`,
                `lastPlansResponse_esim_realtime`,
                `lastPlansResponse_physical_sim`,
                'filteredPlans'
            ];

            const plansCacheKeys = keys.filter(key =>
                key.startsWith('plans_') && !criticalKeys.includes(key)
            ).sort((a, b) => {
                try {
                    const aData = JSON.parse(sessionStorage.getItem(a) || '{}');
                    const bData = JSON.parse(sessionStorage.getItem(b) || '{}');
                    return (bData.timestamp || 0) - (aData.timestamp || 0);
                } catch (e) {
                    return 0;
                }
            });

            if (plansCacheKeys.length > 10) {
                plansCacheKeys.slice(10).forEach(key => {
                    try {
                        sessionStorage.removeItem(key);
                    } catch (e) {
                        console.warn(`Failed to remove old plans cache: ${key}`, e);
                    }
                });
            }

            const planDetailsCacheKeys = keys.filter(key =>
                key.startsWith('plan_') && !key.includes('lastPlansResponse')
            );

            const sortedPlanDetails = planDetailsCacheKeys
                .map(key => {
                    try {
                        const data = JSON.parse(sessionStorage.getItem(key) || '{}');
                        return {
                            key,
                            timestamp: data.timestamp || 0
                        };
                    } catch (e) {
                        return { key, timestamp: 0 };
                    }
                })
                .sort((a, b) => b.timestamp - a.timestamp);

            if (sortedPlanDetails.length > 5) {
                sortedPlanDetails.slice(5).forEach(item => {
                    try {
                        sessionStorage.removeItem(item.key);
                    } catch (e) {
                        console.warn(`Failed to remove old plan details cache: ${item.key}`, e);
                    }
                });
            }
        } catch (e) {
            console.error('Error cleaning up old cache:', e);
        }
    }, []);

    useEffect(() => {
        cleanupOldPlansCache();

        return () => {
            cleanupOldPlansCache();
        };
    }, [cleanupOldPlansCache]);

    const debouncedSearch = useCallback((event) => {
        const query = typeof event === 'string' ? event : event?.target?.value || '';

        isUserInitiatedSearch.current = true;

        setSearchQuery(query);
        setCurrentPage(1);

        if (searchTimeout.current) {
            clearTimeout(searchTimeout.current);
        }

        if (!query.trim()) {
            searchTimeout.current = setTimeout(() => {
                Object.keys(sessionStorage).forEach(key => {
                    if (key.includes('search') || key.startsWith('plans_')) {
                        try {
                            sessionStorage.removeItem(key);
                        } catch (e) {
                            // Ignore errors
                        }
                    }
                });

                const newParams = new URLSearchParams();
                if (selectedCountry !== 'all') {
                    newParams.set('country', selectedCountry);
                }
                if (selectedRegion !== 'all') {
                    newParams.set('region', selectedRegion);
                }
                if (currentTab !== 'esim_realtime') {
                    newParams.set('tab', currentTab);
                }
                setSearchParams(newParams);

                setLoading(true);
                const params = {
                    countryId: selectedCountry === 'all' ? '' : selectedCountry,
                    region: selectedRegion === 'all' ? '' : selectedRegion,
                    category: currentTab,
                    page: 1,
                    limit: 24
                };

                api.get('/api/esim-plans/partner', { params })
                    .then(response => {
                        setFilteredPlans(response.data.plans);
                        setTotalPages(response.data.totalPages);
                        setCurrentPage(1);
                        if (response.data.countries) setCountries(response.data.countries);
                        if (response.data.regions) setRegions(response.data.regions);
                    })
                    .catch(error => {
                        console.error('Error fetching plans after clearing search:', error);
                    })
                    .finally(() => {
                        setLoading(false);
                    });

                isUserInitiatedSearch.current = false;
            }, 0);
            return;
        }

        const delay = (() => {
            const length = query.trim().length;
            if (length < 2) return 800;  // Very short query
            if (length < 3) return 500;  // Short query
            if (length < 5) return 400;  // Medium query
            return 300; // Long, specific query
        })();

        searchTimeout.current = setTimeout(() => {
            const newParams = new URLSearchParams();
            if (selectedCountry !== 'all') {
                newParams.set('country', selectedCountry);
            }
            if (selectedRegion !== 'all') {
                newParams.set('region', selectedRegion);
            }
            if (currentTab !== 'esim_realtime') {
                newParams.set('tab', currentTab);
            }
            // Always add search query
            newParams.set('search', query.trim());
            setSearchParams(newParams);

            fetchPlans(1);
            isUserInitiatedSearch.current = false;
        }, delay);
    }, [fetchPlans, selectedCountry, selectedRegion, currentTab, searchParams, setSearchParams]);


    // Clear cache on unmount
    useEffect(() => {
        return () => {
            if (searchTimeout.current) {
                clearTimeout(searchTimeout.current);
            }
            setFilteredPlans([]);
        };
    }, []);

    // Add memory cleanup on tab change
    useEffect(() => {
        cleanupCache();
    }, [currentTab]);

    // Initialize Socket.io connection for real-time updates
    useEffect(() => {
        const token = localStorage.getItem('token');
        if (token) {
            const socket = io(import.meta.env.VITE_API_URL || 'http://localhost:3000', {
                auth: {
                    token: token
                },
                transports: ['websocket', 'polling']
            });

            socket.on('connect', () => {
                console.log('Partner connected to Socket.io server');
                // Join partner room for plan visibility updates
                if (user?.role === 'partner') {
                    socket.emit('join-partner-room');
                }
            });

            // Handle plan visibility updates
            const handlePlanVisibilityUpdate = (data) => {
                console.log('Received plan visibility update:', data);

                try {
                    const keys = Object.keys(sessionStorage);
                    keys.filter(key =>
                        key.startsWith('plans_') ||
                        key.startsWith('lastPlansResponse_') ||
                        key === 'filteredPlans' ||
                        key.includes('cache')
                    ).forEach(key => {
                        sessionStorage.removeItem(key);
                    });

                    console.log('Cleared all plan cache after WebSocket update');
                } catch (e) {
                    console.warn('Error clearing cache after plan visibility update:', e);
                }

                if (data.type === 'single') {
                    if (data.status === 'hidden') {
                        setFilteredPlans(prevPlans => {
                            const updatedPlans = prevPlans.filter(plan => plan.id !== data.planId);
                            console.log(`Removed plan ${data.planId} from view. Plans count: ${prevPlans.length} -> ${updatedPlans.length}`);
                            return updatedPlans;
                        });

                    }

                    // Only show toast notification if it's not an admin-initiated change
                    if (data.showNotification !== false && data.initiator !== 'admin') {
                        toast({
                            title: "Plan Updated",
                            description: `A plan has been ${data.status === 'visible' ? 'shown' : 'hidden'}.`,
                            duration: 3000,
                        });
                    }
                } else if (data.type === 'provider') {
                    if (data.status === 'hidden') {
                        setFilteredPlans(prevPlans => {
                            const updatedPlans = prevPlans.filter(plan => plan.providerId !== data.providerId);
                            console.log(`Removed provider ${data.providerId} plans from view. Plans count: ${prevPlans.length} -> ${updatedPlans.length}`);
                            return updatedPlans;
                        });
                    }

                    if (data.showNotification !== false && data.initiator !== 'admin') {
                        toast({
                            title: "Provider Plans Updated",
                            description: `All plans from a provider have been ${data.status === 'visible' ? 'shown' : 'hidden'}.`,
                            duration: 3000,
                        });
                    }
                }

                setTimeout(() => {
                    console.log('Forcing complete refresh after WebSocket update');
                    setCurrentPage(1);
                    setLoading(true);
                    fetchPlans(1);
                }, 500);
            };

            socket.on('plan-visibility-updated', handlePlanVisibilityUpdate);

            return () => {
                socket.disconnect();
            };
        }
    }, []); 

    // Handle URL changes when using the browser's back button
    useEffect(() => {
        if (isInitialMount.current) {
            return;
        }

        if (isUserInitiatedSearch.current) {
            return;
        }

        if (returningFromDetails) {
            return;
        }

        const countryParam = searchParams.get('country') || 'all';
        const regionParam = searchParams.get('region') || 'all';
        const tabParam = searchParams.get('tab') || 'esim_realtime';
        const searchParam = searchParams.get('search') || '';
        const pageParam = parseInt(searchParams.get('page') || '1');

        const countryChanged = countryParam !== selectedCountry;
        const regionChanged = regionParam !== selectedRegion;
        const tabChanged = tabParam !== currentTab;
        const searchChanged = searchParam !== searchQuery;
        const pageChanged = pageParam !== currentPage;

        if (countryChanged || regionChanged || tabChanged || searchChanged || pageChanged) {
            setSelectedCountry(countryParam);
            setSelectedRegion(regionParam);
            setCurrentTab(tabParam);
            setSearchQuery(searchParam);
            setCurrentPage(pageParam);

            fetchPlans(pageParam);
        }
    }, [searchParams.toString(), selectedCountry, selectedRegion, currentTab, searchQuery, currentPage, returningFromDetails]);  // Add dependencies to prevent stale closures

    const handlePageChange = (newPage) => {
        setCurrentPage(newPage);
        fetchPlans(newPage);
        updateSearchParams({
            country: selectedCountry,
            region: selectedRegion,
            tab: currentTab,
            page: newPage,
            search: (searchQuery || '').trim()
        });
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    };

    const clearSearch = () => {
        if (searchTimeout.current) {
            clearTimeout(searchTimeout.current);
        }

        Object.keys(sessionStorage).forEach(key => {
            if (key.includes('search') || key.startsWith('plans_')) {
                try {
                    sessionStorage.removeItem(key);
                } catch (e) {
                    // Ignore errors
                }
            }
        });

        // Remove search-related session storage items
        sessionStorage.removeItem('lastSearchQuery');
        sessionStorage.removeItem('fromPlanDetails');
        sessionStorage.removeItem('searchCleared');

        setReturningFromDetails(false);

        setSearchQuery('');
        setCurrentPage(1);

        const newParams = new URLSearchParams();
        if (selectedCountry !== 'all') {
            newParams.set('country', selectedCountry);
        }
        if (selectedRegion !== 'all') {
            newParams.set('region', selectedRegion);
        }
        if (currentTab !== 'esim_realtime') {
            newParams.set('tab', currentTab);
        }
        setSearchParams(newParams);

        setLoading(true);
        const params = {
            countryId: selectedCountry === 'all' ? '' : selectedCountry,
            region: selectedRegion === 'all' ? '' : selectedRegion,
            category: currentTab,
            page: 1,
            limit: 24
        };

        api.get('/api/esim-plans/partner', { params })
            .then(response => {
                setFilteredPlans(response.data.plans);
                setTotalPages(response.data.totalPages);
                setCurrentPage(1);
                if (response.data.countries) setCountries(response.data.countries);
                if (response.data.regions) setRegions(response.data.regions);
            })
            .catch(error => {
                console.error('Error fetching plans after clearing search:', error);
                toast({
                    variant: "destructive",
                    title: "Error",
                    description: "Failed to fetch plans. Please try again.",
                });
            })
            .finally(() => {
                setLoading(false);
            });
    };

    // Memoize the clear filters function
    const clearFilters = useCallback(() => {
        if (searchTimeout.current) {
            clearTimeout(searchTimeout.current);
        }

        // Clear all search-related session storage
        sessionStorage.removeItem('lastSearchQuery');
        sessionStorage.removeItem('fromPlanDetails');
        sessionStorage.removeItem('lastDetailViewTime');
        sessionStorage.removeItem('returningToDefaultView');
        sessionStorage.removeItem('searchCleared');

        Object.keys(sessionStorage).forEach(key => {
            if (key.startsWith('plans_') || key.includes('filteredPlans')) {
                try {
                    sessionStorage.removeItem(key);
                } catch (e) {
                    // Ignore errors
                }
            }
        });

        setReturningFromDetails(false);

        setSelectedCountry('all');
        setSelectedRegion('all');
        setCurrentTab('esim_realtime');
        setSearchQuery('');
        setCurrentPage(1);

        setSearchParams({});

        setLoading(true);
        const params = {
            countryId: '',
            region: '',
            category: 'esim_realtime',
            page: 1,
            limit: 24
        };

        api.get('/api/esim-plans/partner', { params })
            .then(response => {
                setFilteredPlans(response.data.plans);
                setTotalPages(response.data.totalPages);
                setCurrentPage(1);
                if (response.data.countries) setCountries(response.data.countries);
                if (response.data.regions) setRegions(response.data.regions);
            })
            .catch(error => {
                console.error('Error fetching plans after clearing filters:', error);
                toast({
                    variant: "destructive",
                    title: "Error",
                    description: "Failed to fetch plans. Please try again.",
                });
            })
            .finally(() => {
                setLoading(false);
            });
    }, [setSearchParams, toast]);

    const handleAddToCart = async (plan) => {
      try {
            await api.post('/api/cart/add', { planId: plan.id });
          toast({
                title: "Success",
                description: "Plan added to cart successfully!",
          });
      } catch (error) {
          console.error('Error adding to cart:', error);
          toast({
                variant: "destructive",
                title: "Error",
                description: error.response?.data?.message || "Failed to add plan to cart.",
          });
      }
  };

    // Pagination component
    const Pagination = () => {
        const pages = [];
        const maxVisible = 5;

        if (totalPages <= maxVisible) {
            for (let i = 1; i <= totalPages; i++) {
                pages.push(i);
            }
        } else {
            pages.push(1);

            let start = Math.max(2, currentPage - Math.floor(maxVisible / 2));
            let end = Math.min(totalPages - 1, start + maxVisible - 3);

            start = Math.max(2, end - (maxVisible - 3));

            if (start > 2) {
                pages.push('...');
            }

            for (let i = start; i <= end; i++) {
                pages.push(i);
            }

            if (end < totalPages - 1) {
                pages.push('...');
            }

            pages.push(totalPages);
        }

        return (
            <div className="flex items-center justify-center gap-2 mt-8">
                <Button
                    variant="outline"
                    size="icon"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                >
                    <ChevronLeft className="h-4 w-4" />
                </Button>

                {pages.map((page, index) => (
                    page === '...' ? (
                        <span key={`ellipsis-${index}`} className="px-2">...</span>
                    ) : (
                        <Button
                            key={page}
                            variant={currentPage === page ? "default" : "outline"}
                            onClick={() => handlePageChange(page)}
                            className={currentPage === page ? "bg-blue-600" : ""}
                        >
                            {page}
                        </Button>
                    )
                ))}

                <Button
                    variant="outline"
                    size="icon"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                >
                    <ChevronRight className="h-4 w-4" />
                </Button>
            </div>
        );
  };

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
            <div className="container mx-auto px-4 py-8 max-w-7xl">
                {/* Modern Header Section */}
                <div className="mb-8">
                    {/* Header with Search Bar */}
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6 mb-6">
                        {/* Title and Tagline */}
                        <div className="space-y-2">
                            <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-600 bg-clip-text text-transparent">
                                eSIM Plans
                            </h1>
                            <p className="text-lg text-slate-600 font-medium">
                                Browse Your Plans
                            </p>
                        </div>

                        {/* Enhanced Search Bar */}
                        <div className="relative md:w-96">
                            <div className="relative group">
                                <Input
                                    type="text"
                                    placeholder="Search plans, countries, regions..."
                                    value={searchQuery || ''}
                                    onChange={(e) => debouncedSearch(e)}
                                    className="w-full h-12 pl-12 pr-12 rounded-full border-2 border-slate-200 bg-white/80 backdrop-blur-sm shadow-lg hover:shadow-xl focus:shadow-xl focus:border-blue-400 focus:ring-4 focus:ring-blue-100 transition-all duration-300 text-slate-700 placeholder:text-slate-400 group-hover:scale-[1.02] focus:scale-[1.02]"
                                />
                                <Search className="w-5 h-5 absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400 group-hover:text-blue-500 transition-colors duration-300" />
                                {searchQuery && (
                                    <button
                                        onClick={clearSearch}
                                        className="absolute right-4 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-red-500 transition-colors duration-300 hover:scale-110"
                                    >
                                        <X className="w-5 h-5" />
                                    </button>
                                )}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Modern Tabs and Filters Section */}
                <div className="bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6 mb-8">
                    <Tabs defaultValue="esim_realtime" className="w-full" onValueChange={(value) => {
                        handleFilterChange('tab', value);
                    }}>
                        <TabsList className="bg-slate-100/80 p-1 rounded-xl mb-6">
                            <TabsTrigger
                                value="esim_realtime"
                                className="focus:outline-none data-[state=active]:bg-white data-[state=active]:shadow-md rounded-lg px-6 py-2 font-medium transition-all duration-300"
                            >
                                Regular Plans
                            </TabsTrigger>
                            {/* <TabsTrigger value="esim_addon" className="focus:outline-none">Top-up Plans</TabsTrigger> */}
                        </TabsList>

                        {/* Enhanced Filter Pills */}
                        <div className="flex flex-wrap items-center gap-3">
                            {/* Region Filter Pill */}
                            <div className="relative">
                                <Select value={selectedRegion} onValueChange={(value) => handleFilterChange('region', value)}>
                                    <SelectTrigger className="min-w-[140px] h-11 rounded-full bg-white/80 border-2 border-slate-200 hover:border-blue-300 focus:border-blue-400 focus:ring-4 focus:ring-blue-100 transition-all duration-300 shadow-sm hover:shadow-md">
                                        <SelectValue placeholder="All Regions" />
                                    </SelectTrigger>
                                    <SelectContent className="max-h-[400px] overflow-y-auto rounded-xl border-2 border-slate-200 shadow-xl">
                                        <SelectItem value="all" className="rounded-lg">All Regions</SelectItem>
                                        {regions.map((region) => (
                                            <SelectItem key={region} value={region} className="rounded-lg">
                                                {region}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>

                            {/* Country Filter Pill */}
                            <div className="relative">
                                <Select value={selectedCountry} onValueChange={(value) => handleFilterChange('country', value)}>
                                    <SelectTrigger className="min-w-[140px] h-11 rounded-full bg-white/80 border-2 border-slate-200 hover:border-blue-300 focus:border-blue-400 focus:ring-4 focus:ring-blue-100 transition-all duration-300 shadow-sm hover:shadow-md">
                                        <SelectValue placeholder="All Countries" />
                                    </SelectTrigger>
                                    <SelectContent className="max-h-[400px] overflow-y-auto rounded-xl border-2 border-slate-200 shadow-xl">
                                        <SelectItem value="all" className="rounded-lg">All Countries</SelectItem>
                                        {countries.map((country) => (
                                            <SelectItem key={country.id} value={country.id} className="rounded-lg">
                                                <div className="flex items-center gap-2">
                                                    <img
                                                        src={country.flagUrl || `https://flagcdn.com/w20/${country.id.toLowerCase()}.png`}
                                                        alt={`${country.name} flag`}
                                                        className="w-4 h-3 object-cover rounded-sm"
                                                        onError={(e) => {
                                                            e.target.onerror = null;
                                                            e.target.src = 'https://upload.wikimedia.org/wikipedia/commons/thumb/b/b0/No_flag.svg/32px-No_flag.svg.png';
                                                        }}
                                                    />
                                                    {country.name}
                                                </div>
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>

                            {/* Clear Filters Button */}
                            {(selectedCountry !== 'all' || selectedRegion !== 'all' || searchQuery) && (
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={clearFilters}
                                    className="h-11 px-6 rounded-full border-2 border-slate-200 hover:border-red-300 hover:bg-red-50 hover:text-red-600 transition-all duration-300 shadow-sm hover:shadow-md min-w-[44px]"
                                >
                                    Clear Filters
                                </Button>
                            )}
                        </div>

                        <TabsContent value="esim_realtime" className="mt-8">
                            {loading ? (
                                <div className="space-y-8">
                                    <div className="text-center py-8">
                                        <div className="inline-flex items-center gap-3 text-slate-600 bg-white/80 backdrop-blur-sm px-6 py-3 rounded-full shadow-lg">
                                            <Loader2 className="w-6 h-6 animate-spin text-blue-500" />
                                            <span className="font-medium">Loading plans...</span>
                                        </div>
                                    </div>
                                    {/* Enhanced Loading Skeleton */}
                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
                                        {[...Array(12)].map((_, index) => (
                                            <Card key={index} className="animate-pulse rounded-2xl border-0 shadow-lg bg-white/60 backdrop-blur-sm">
                                                <div className="h-2 bg-gradient-to-r from-blue-200 to-indigo-200 rounded-t-2xl"></div>
                                                <div className="p-6 space-y-4">
                                                    <div className="flex justify-between items-start">
                                                        <div className="space-y-3">
                                                            <div className="h-4 bg-slate-200 rounded-full w-20"></div>
                                                            <div className="h-6 bg-slate-200 rounded-full w-32"></div>
                                                        </div>
                                                        <div className="h-10 w-10 bg-slate-200 rounded-xl"></div>
                                                    </div>
                                                    <div className="space-y-3">
                                                        <div className="h-8 bg-slate-200 rounded-full w-24"></div>
                                                        <div className="h-4 bg-slate-200 rounded-full w-16"></div>
                                                    </div>
                                                    <div className="space-y-3">
                                                        <div className="h-12 bg-slate-100 rounded-xl"></div>
                                                        <div className="h-8 bg-slate-100 rounded-xl"></div>
                                                        <div className="h-8 bg-slate-100 rounded-xl"></div>
                                                    </div>
                                                    <div className="h-12 bg-blue-200 rounded-xl"></div>
                                                </div>
                                            </Card>
                                        ))}
                                    </div>
                                </div>
                            ) : filteredPlans.length > 0 ? (
                                <>
                                    {/* Enhanced Plans Grid */}
                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
                                        {filteredPlans
                                            .filter(plan => plan && (plan.id || plan.productId || plan.externalProductId))
                                            .map((plan, index) => (
                                            <PlanCard
                                                key={plan.id || plan.productId || plan.externalProductId || `plan-${index}`}
                                                plan={plan}
                                                onAddToCart={handleAddToCart}
                                                navigate={navigate}
                                            />
                                        ))}
                                    </div>
                                    {totalPages > 1 && <Pagination />}
                                </>
                            ) : (
                                /* Enhanced Empty State */
                                <div className="text-center py-16">
                                    <div className="bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-12 max-w-md mx-auto">
                                        <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-full flex items-center justify-center mx-auto mb-6">
                                            <Search className="w-8 h-8 text-blue-500" />
                                        </div>
                                        <h3 className="text-xl font-semibold text-slate-700 mb-3">No Plans Available</h3>
                                        <p className="text-slate-500 mb-6">We couldn't find any plans matching your criteria. Try adjusting your filters or search terms.</p>
                                        {(selectedCountry !== 'all' || selectedRegion !== 'all' || searchQuery) && (
                                            <Button
                                                onClick={clearFilters}
                                                className="bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white rounded-full px-6 py-2 transition-all duration-300 shadow-lg hover:shadow-xl"
                                            >
                                                Clear All Filters
                                            </Button>
                                        )}
                                    </div>
                                </div>
                            )}
                        </TabsContent>

                        <TabsContent value="esim_addon" className="mt-8">
                            {loading ? (
                                <div className="flex justify-center items-center h-32">
                                    <div className="inline-flex items-center gap-3 text-slate-600 bg-white/80 backdrop-blur-sm px-6 py-3 rounded-full shadow-lg">
                                        <Loader2 className="w-6 h-6 animate-spin text-blue-500" />
                                        <span className="font-medium">Loading top-up plans...</span>
                                    </div>
                                </div>
                            ) : (
                                <>
                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 mt-6">
                                        {filteredPlans.filter(plan => plan.category === currentTab).length === 0 ? (
                                            <div className="col-span-full text-center py-16">
                                                <div className="bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-12 max-w-md mx-auto">
                                                    <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-full flex items-center justify-center mx-auto mb-6">
                                                        <Search className="w-8 h-8 text-blue-500" />
                                                    </div>
                                                    <h3 className="text-xl font-semibold text-slate-700 mb-3">No Top-up Plans Available</h3>
                                                    <p className="text-slate-500">No top-up plans found for the selected filters.</p>
                                                </div>
                                            </div>
                                        ) : (
                                            filteredPlans
                                                .filter(plan => plan && plan.category === currentTab && (plan.id || plan.productId || plan.externalProductId))
                                                .map((plan, index) => (
                                                <PlanCard
                                                    key={`${plan.id || plan.productId || plan.externalProductId || `plan-${index}`}-${plan.provider?.name || 'local'}-${plan.category}-${index}`}
                                                    plan={plan}
                                                    onAddToCart={handleAddToCart}
                                                    navigate={navigate}
                                                />
                                            ))
                                        )}
                                    </div>
                                    {totalPages > 1 && <Pagination />}
                                </>
                            )}
                        </TabsContent>
                    </Tabs>
                </div>
            </div>
        </div>
    );
}
